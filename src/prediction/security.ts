/**
 * Security vulnerability scanning - Detect potential security issues
 */

import { CodebaseAnalysis, FileAnalysis, FunctionMetadata } from "../analysis/types.js";
import { 
  SecurityScanResult,
  SecurityVulnerabilityDetailed,
  SecurityThreat,
  ComplianceAssessment,
  SecurityRecommendation,
  SecurityTrend,
  ExploitationAnalysis,
  RemediationPlan
} from "./types.js";

/**
 * Perform comprehensive security scan
 */
export async function scanSecurity(analysis: CodebaseAnalysis): Promise<SecurityScanResult> {
  console.log(`🔒 Scanning for security vulnerabilities...`);
  
  const vulnerabilities = detectVulnerabilities(analysis);
  const threats = identifySecurityThreats(analysis);
  const compliance = assessCompliance(analysis);
  const recommendations = generateSecurityRecommendations(vulnerabilities, threats);
  const trends = analyzeSecurityTrends(analysis);
  
  const overallRisk = calculateOverallSecurityRisk(vulnerabilities, threats);
  
  console.log(`🛡️ Found ${vulnerabilities.length} vulnerabilities, ${threats.length} threats`);
  
  return {
    overallRisk,
    vulnerabilities,
    threats,
    compliance,
    recommendations,
    trends
  };
}

/**
 * Detect security vulnerabilities in code
 */
function detectVulnerabilities(analysis: CodebaseAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const file of analysis.files) {
    // SQL Injection vulnerabilities
    vulnerabilities.push(...detectSQLInjection(file));
    
    // XSS vulnerabilities
    vulnerabilities.push(...detectXSS(file));
    
    // Path Traversal vulnerabilities
    vulnerabilities.push(...detectPathTraversal(file));
    
    // Insecure Cryptography
    vulnerabilities.push(...detectInsecureCrypto(file));
    
    // Authentication/Authorization issues
    vulnerabilities.push(...detectAuthIssues(file));
    
    // Input Validation issues
    vulnerabilities.push(...detectInputValidation(file));
    
    // Sensitive Data Exposure
    vulnerabilities.push(...detectSensitiveDataExposure(file));
  }
  
  return vulnerabilities;
}

/**
 * Detect SQL Injection vulnerabilities
 */
function detectSQLInjection(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const func of file.functions) {
    // Look for string concatenation in SQL queries
    const hasSQLCalls = func.calls.some(call => 
      call.includes('query') || 
      call.includes('execute') || 
      call.includes('sql') ||
      call.includes('SELECT') ||
      call.includes('INSERT') ||
      call.includes('UPDATE') ||
      call.includes('DELETE')
    );
    
    if (hasSQLCalls) {
      // Check for potential string concatenation patterns
      const hasStringConcat = func.calls.some(call => 
        call.includes('+') || call.includes('concat') || call.includes('${')
      );
      
      if (hasStringConcat) {
        vulnerabilities.push({
          id: `sql-injection-${file.path}-${func.name}`,
          type: 'SQL Injection',
          severity: 'high',
          cvss: 8.1,
          cwe: 'CWE-89',
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          description: `Potential SQL injection vulnerability in function "${func.name}" due to string concatenation in SQL queries`,
          exploitation: {
            difficulty: 'easy',
            prerequisites: ['User input access', 'Database connection'],
            impact: 'Data breach, unauthorized access, data manipulation',
            likelihood: 0.8,
            attackVectors: [
              'Form input manipulation',
              'URL parameter injection',
              'API payload injection'
            ]
          },
          remediation: {
            priority: 'urgent',
            effort: 'small',
            steps: [
              'Replace string concatenation with parameterized queries',
              'Use prepared statements or ORM query builders',
              'Implement input validation and sanitization',
              'Add SQL injection testing to test suite'
            ],
            timeline: '1-2 days',
            verification: [
              'Code review for parameterized queries',
              'Automated security testing',
              'Manual penetration testing'
            ]
          },
          references: [
            'https://owasp.org/www-community/attacks/SQL_Injection',
            'https://cwe.mitre.org/data/definitions/89.html'
          ]
        });
      }
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect XSS vulnerabilities
 */
function detectXSS(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const func of file.functions) {
    // Look for DOM manipulation without sanitization
    const hasDOMManipulation = func.calls.some(call => 
      call.includes('innerHTML') || 
      call.includes('outerHTML') || 
      call.includes('document.write') ||
      call.includes('eval') ||
      call.includes('dangerouslySetInnerHTML')
    );
    
    if (hasDOMManipulation) {
      vulnerabilities.push({
        id: `xss-${file.path}-${func.name}`,
        type: 'Cross-Site Scripting (XSS)',
        severity: 'medium',
        cvss: 6.1,
        cwe: 'CWE-79',
        location: {
          file: file.path,
          function: func.name,
          line: func.startLine
        },
        description: `Potential XSS vulnerability in function "${func.name}" due to unsafe DOM manipulation`,
        exploitation: {
          difficulty: 'medium',
          prerequisites: ['User input reflection', 'Web browser context'],
          impact: 'Session hijacking, credential theft, malicious redirects',
          likelihood: 0.6,
          attackVectors: [
            'Reflected XSS via URL parameters',
            'Stored XSS via user content',
            'DOM-based XSS via client-side scripts'
          ]
        },
        remediation: {
          priority: 'high',
          effort: 'small',
          steps: [
            'Replace innerHTML with textContent or safe DOM methods',
            'Implement proper input sanitization',
            'Use Content Security Policy (CSP)',
            'Add XSS protection headers'
          ],
          timeline: '2-3 days',
          verification: [
            'Code review for safe DOM manipulation',
            'XSS testing with payloads',
            'Browser security header validation'
          ]
        },
        references: [
          'https://owasp.org/www-community/attacks/xss/',
          'https://cwe.mitre.org/data/definitions/79.html'
        ]
      });
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect Path Traversal vulnerabilities
 */
function detectPathTraversal(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const func of file.functions) {
    // Look for file operations with user input
    const hasFileOps = func.calls.some(call => 
      call.includes('readFile') || 
      call.includes('writeFile') || 
      call.includes('createReadStream') ||
      call.includes('createWriteStream') ||
      call.includes('open') ||
      call.includes('access')
    );
    
    if (hasFileOps) {
      // Check for potential path concatenation
      const hasPathConcat = func.calls.some(call => 
        call.includes('path.join') || call.includes('+') || call.includes('${')
      );
      
      if (hasPathConcat) {
        vulnerabilities.push({
          id: `path-traversal-${file.path}-${func.name}`,
          type: 'Path Traversal',
          severity: 'medium',
          cvss: 5.3,
          cwe: 'CWE-22',
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          description: `Potential path traversal vulnerability in function "${func.name}" due to unsafe file path construction`,
          exploitation: {
            difficulty: 'medium',
            prerequisites: ['File system access', 'User input in file paths'],
            impact: 'Unauthorized file access, information disclosure',
            likelihood: 0.5,
            attackVectors: [
              'Directory traversal with ../ sequences',
              'Absolute path injection',
              'Symbolic link attacks'
            ]
          },
          remediation: {
            priority: 'medium',
            effort: 'small',
            steps: [
              'Validate and sanitize file paths',
              'Use path.resolve() and check against allowed directories',
              'Implement file access controls',
              'Use chroot or sandboxing'
            ],
            timeline: '1-2 days',
            verification: [
              'Path validation testing',
              'Directory traversal attack simulation',
              'File access permission verification'
            ]
          },
          references: [
            'https://owasp.org/www-community/attacks/Path_Traversal',
            'https://cwe.mitre.org/data/definitions/22.html'
          ]
        });
      }
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect insecure cryptography
 */
function detectInsecureCrypto(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const func of file.functions) {
    // Look for weak cryptographic functions
    const hasWeakCrypto = func.calls.some(call => 
      call.includes('md5') || 
      call.includes('sha1') || 
      call.includes('des') ||
      call.includes('rc4') ||
      call.includes('Math.random')
    );
    
    if (hasWeakCrypto) {
      vulnerabilities.push({
        id: `weak-crypto-${file.path}-${func.name}`,
        type: 'Weak Cryptography',
        severity: 'medium',
        cvss: 5.9,
        cwe: 'CWE-327',
        location: {
          file: file.path,
          function: func.name,
          line: func.startLine
        },
        description: `Weak cryptographic algorithm detected in function "${func.name}"`,
        exploitation: {
          difficulty: 'hard',
          prerequisites: ['Cryptographic knowledge', 'Computational resources'],
          impact: 'Data decryption, authentication bypass',
          likelihood: 0.3,
          attackVectors: [
            'Brute force attacks',
            'Rainbow table attacks',
            'Collision attacks'
          ]
        },
        remediation: {
          priority: 'medium',
          effort: 'medium',
          steps: [
            'Replace weak algorithms with strong alternatives (SHA-256, AES)',
            'Use cryptographically secure random number generators',
            'Implement proper key management',
            'Add cryptographic testing'
          ],
          timeline: '3-5 days',
          verification: [
            'Cryptographic algorithm review',
            'Security library validation',
            'Key strength verification'
          ]
        },
        references: [
          'https://owasp.org/www-community/vulnerabilities/Insecure_Cryptographic_Storage',
          'https://cwe.mitre.org/data/definitions/327.html'
        ]
      });
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect authentication/authorization issues
 */
function detectAuthIssues(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  // Look for hardcoded credentials
  for (const func of file.functions) {
    const hasHardcodedCreds = func.calls.some(call => 
      call.includes('password') || 
      call.includes('secret') || 
      call.includes('token') ||
      call.includes('api_key') ||
      call.includes('private_key')
    );
    
    if (hasHardcodedCreds) {
      vulnerabilities.push({
        id: `hardcoded-creds-${file.path}-${func.name}`,
        type: 'Hardcoded Credentials',
        severity: 'high',
        cvss: 7.5,
        cwe: 'CWE-798',
        location: {
          file: file.path,
          function: func.name,
          line: func.startLine
        },
        description: `Potential hardcoded credentials in function "${func.name}"`,
        exploitation: {
          difficulty: 'trivial',
          prerequisites: ['Source code access'],
          impact: 'Unauthorized access, privilege escalation',
          likelihood: 0.9,
          attackVectors: [
            'Source code analysis',
            'Binary reverse engineering',
            'Configuration file inspection'
          ]
        },
        remediation: {
          priority: 'urgent',
          effort: 'small',
          steps: [
            'Move credentials to environment variables',
            'Use secure credential management systems',
            'Implement proper secret rotation',
            'Add credential scanning to CI/CD'
          ],
          timeline: '1 day',
          verification: [
            'Code review for hardcoded secrets',
            'Automated secret scanning',
            'Environment configuration validation'
          ]
        },
        references: [
          'https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password',
          'https://cwe.mitre.org/data/definitions/798.html'
        ]
      });
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect input validation issues
 */
function detectInputValidation(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  // Look for functions that process user input without validation
  for (const func of file.functions) {
    const hasUserInput = func.parameters.some(param => 
      param.name.includes('input') || 
      param.name.includes('data') || 
      param.name.includes('request') ||
      param.name.includes('body') ||
      param.name.includes('query')
    );
    
    if (hasUserInput && func.parameters.length > 0) {
      // Check if there's any validation
      const hasValidation = func.calls.some(call => 
        call.includes('validate') || 
        call.includes('sanitize') || 
        call.includes('escape') ||
        call.includes('filter') ||
        call.includes('check')
      );
      
      if (!hasValidation) {
        vulnerabilities.push({
          id: `input-validation-${file.path}-${func.name}`,
          type: 'Insufficient Input Validation',
          severity: 'medium',
          cvss: 5.3,
          cwe: 'CWE-20',
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          description: `Function "${func.name}" processes user input without proper validation`,
          exploitation: {
            difficulty: 'easy',
            prerequisites: ['User input access'],
            impact: 'Data corruption, injection attacks, system compromise',
            likelihood: 0.7,
            attackVectors: [
              'Malformed input injection',
              'Buffer overflow attempts',
              'Type confusion attacks'
            ]
          },
          remediation: {
            priority: 'medium',
            effort: 'small',
            steps: [
              'Implement input validation for all user inputs',
              'Use validation libraries or frameworks',
              'Add input sanitization',
              'Implement proper error handling'
            ],
            timeline: '2-3 days',
            verification: [
              'Input validation testing',
              'Fuzzing with malformed inputs',
              'Boundary value testing'
            ]
          },
          references: [
            'https://owasp.org/www-community/vulnerabilities/Improper_Input_Validation',
            'https://cwe.mitre.org/data/definitions/20.html'
          ]
        });
      }
    }
  }
  
  return vulnerabilities;
}

/**
 * Detect sensitive data exposure
 */
function detectSensitiveDataExposure(file: FileAnalysis): SecurityVulnerabilityDetailed[] {
  const vulnerabilities: SecurityVulnerabilityDetailed[] = [];
  
  for (const func of file.functions) {
    // Look for logging or output of sensitive data
    const hasLogging = func.calls.some(call => 
      call.includes('console.log') || 
      call.includes('logger') || 
      call.includes('print') ||
      call.includes('debug') ||
      call.includes('error')
    );
    
    const hasSensitiveData = func.calls.some(call => 
      call.includes('password') || 
      call.includes('token') || 
      call.includes('secret') ||
      call.includes('credit') ||
      call.includes('ssn')
    );
    
    if (hasLogging && hasSensitiveData) {
      vulnerabilities.push({
        id: `data-exposure-${file.path}-${func.name}`,
        type: 'Sensitive Data Exposure',
        severity: 'medium',
        cvss: 4.3,
        cwe: 'CWE-200',
        location: {
          file: file.path,
          function: func.name,
          line: func.startLine
        },
        description: `Potential sensitive data exposure in logs in function "${func.name}"`,
        exploitation: {
          difficulty: 'medium',
          prerequisites: ['Log file access', 'System access'],
          impact: 'Information disclosure, privacy violation',
          likelihood: 0.4,
          attackVectors: [
            'Log file analysis',
            'Error message inspection',
            'Debug output examination'
          ]
        },
        remediation: {
          priority: 'medium',
          effort: 'small',
          steps: [
            'Remove sensitive data from logs',
            'Implement data masking for logging',
            'Use structured logging with filtering',
            'Review and secure log storage'
          ],
          timeline: '1-2 days',
          verification: [
            'Log content review',
            'Data masking validation',
            'Log access control verification'
          ]
        },
        references: [
          'https://owasp.org/www-community/vulnerabilities/Information_exposure_through_log_files',
          'https://cwe.mitre.org/data/definitions/200.html'
        ]
      });
    }
  }
  
  return vulnerabilities;
}

/**
 * Identify security threats
 */
function identifySecurityThreats(analysis: CodebaseAnalysis): SecurityThreat[] {
  const threats: SecurityThreat[] = [];
  
  // Dependency vulnerabilities threat
  if (analysis.dependencies.metrics.totalDependencies > 50) {
    threats.push({
      type: 'Dependency Vulnerabilities',
      description: `Large number of dependencies (${analysis.dependencies.metrics.totalDependencies}) increases vulnerability surface`,
      likelihood: 0.7,
      impact: 'high',
      mitigations: [
        'Regular dependency updates',
        'Vulnerability scanning',
        'Dependency audit',
        'Minimal dependency principle'
      ],
      monitoring: [
        'Automated vulnerability scanning',
        'Dependency update notifications',
        'Security advisory monitoring'
      ]
    });
  }
  
  // Code complexity threat
  if (analysis.quality.maintainability.complexity > 20) {
    threats.push({
      type: 'Code Complexity Security Risk',
      description: 'High code complexity increases likelihood of security bugs',
      likelihood: 0.6,
      impact: 'medium',
      mitigations: [
        'Code refactoring',
        'Security code review',
        'Automated testing',
        'Static analysis'
      ],
      monitoring: [
        'Complexity metrics tracking',
        'Security testing coverage',
        'Code review metrics'
      ]
    });
  }
  
  return threats;
}

/**
 * Assess compliance with security standards
 */
function assessCompliance(analysis: CodebaseAnalysis): ComplianceAssessment[] {
  return [
    {
      standard: 'OWASP Top 10',
      status: 'partial',
      score: 70,
      gaps: [
        {
          requirement: 'A01:2021 – Broken Access Control',
          status: 'partial',
          description: 'Some access control mechanisms in place but not comprehensive',
          remediation: 'Implement comprehensive authorization checks',
          priority: 'high'
        },
        {
          requirement: 'A03:2021 – Injection',
          status: 'missing',
          description: 'No systematic protection against injection attacks',
          remediation: 'Implement input validation and parameterized queries',
          priority: 'high'
        }
      ],
      recommendations: [
        'Implement comprehensive security testing',
        'Add security headers',
        'Establish security development lifecycle'
      ]
    }
  ];
}

/**
 * Generate security recommendations
 */
function generateSecurityRecommendations(
  vulnerabilities: SecurityVulnerabilityDetailed[],
  threats: SecurityThreat[]
): SecurityRecommendation[] {
  const recommendations: SecurityRecommendation[] = [];
  
  if (vulnerabilities.length > 0) {
    recommendations.push({
      category: 'prevention',
      priority: 'high',
      description: 'Implement secure coding practices',
      implementation: [
        'Establish secure coding guidelines',
        'Implement code review process',
        'Add security testing to CI/CD',
        'Provide security training'
      ],
      tools: ['Static analysis tools', 'Security scanners', 'Code review tools'],
      timeline: '2-4 weeks'
    });
  }
  
  recommendations.push({
    category: 'detection',
    priority: 'medium',
    description: 'Implement security monitoring',
    implementation: [
      'Set up security logging',
      'Implement intrusion detection',
      'Add vulnerability scanning',
      'Monitor security metrics'
    ],
    tools: ['SIEM systems', 'Vulnerability scanners', 'Log analysis tools'],
    timeline: '3-6 weeks'
  });
  
  return recommendations;
}

/**
 * Analyze security trends
 */
function analyzeSecurityTrends(analysis: CodebaseAnalysis): SecurityTrend[] {
  return [
    {
      metric: 'Vulnerability Count',
      direction: 'stable',
      timeframe: 'last 3 months',
      significance: 'medium'
    },
    {
      metric: 'Security Test Coverage',
      direction: 'improving',
      timeframe: 'last month',
      significance: 'high'
    }
  ];
}

/**
 * Calculate overall security risk
 */
function calculateOverallSecurityRisk(
  vulnerabilities: SecurityVulnerabilityDetailed[],
  threats: SecurityThreat[]
): 'low' | 'medium' | 'high' | 'critical' {
  const criticalVulns = vulnerabilities.filter(v => v.severity === 'critical').length;
  const highVulns = vulnerabilities.filter(v => v.severity === 'high').length;
  const highThreats = threats.filter(t => t.impact === 'critical' || t.impact === 'high').length;
  
  if (criticalVulns > 0) {
    return 'critical';
  } else if (highVulns > 2 || highThreats > 2) {
    return 'high';
  } else if (highVulns > 0 || highThreats > 0 || vulnerabilities.length > 5) {
    return 'medium';
  } else {
    return 'low';
  }
}
