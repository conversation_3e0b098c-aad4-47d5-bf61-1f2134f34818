/**
 * Type definitions for predictive analysis system
 */

export interface PredictionAnalysis {
  issues: IssuePrediction[];
  performance: PerformanceRiskAssessment;
  security: SecurityScanResult;
  technicalDebt: TechnicalDebtMeasurement;
  summary: PredictionSummary;
}

export interface IssuePrediction {
  id: string;
  type: IssueType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence: number; // 0-1
  probability: number; // 0-1
  timeframe: 'immediate' | 'short-term' | 'medium-term' | 'long-term';
  description: string;
  location: IssueLocation;
  impact: IssueImpact;
  prevention: PreventionStrategy;
  relatedIssues: string[];
}

export type IssueType = 
  | 'bug-prone-code'
  | 'performance-degradation'
  | 'security-vulnerability'
  | 'maintainability-decline'
  | 'scalability-bottleneck'
  | 'dependency-conflict'
  | 'technical-debt-accumulation'
  | 'test-coverage-gap'
  | 'documentation-drift'
  | 'architecture-violation';

export interface IssueLocation {
  file: string;
  function?: string;
  class?: string;
  line?: number;
  module?: string;
}

export interface IssueImpact {
  scope: 'local' | 'module' | 'system' | 'global';
  affectedFiles: string[];
  userImpact: 'none' | 'minor' | 'moderate' | 'major' | 'critical';
  businessImpact: 'none' | 'low' | 'medium' | 'high' | 'critical';
  technicalImpact: 'none' | 'low' | 'medium' | 'high' | 'critical';
}

export interface PreventionStrategy {
  actions: PreventionAction[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  effort: 'minimal' | 'small' | 'medium' | 'large' | 'extensive';
  timeline: string;
  resources: string[];
}

export interface PreventionAction {
  type: 'refactor' | 'test' | 'document' | 'monitor' | 'upgrade' | 'review';
  description: string;
  steps: string[];
  tools: string[];
  estimatedHours: number;
}

export interface PerformanceRiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  bottlenecks: PerformanceBottleneck[];
  scalabilityIssues: ScalabilityIssue[];
  resourceUsage: ResourceUsageAnalysis;
  optimizationOpportunities: PerformanceOptimization[];
  trends: PerformanceTrend[];
}

export interface PerformanceBottleneck {
  id: string;
  type: 'cpu' | 'memory' | 'io' | 'network' | 'database' | 'algorithm';
  location: IssueLocation;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metrics: PerformanceMetrics;
  threshold: PerformanceThreshold;
  prediction: BottleneckPrediction;
}

export interface PerformanceMetrics {
  currentValue: number;
  unit: string;
  baseline: number;
  trend: 'improving' | 'stable' | 'degrading';
  variance: number;
}

export interface PerformanceThreshold {
  warning: number;
  critical: number;
  target: number;
}

export interface BottleneckPrediction {
  timeToThreshold: string;
  confidence: number;
  factors: string[];
  mitigations: string[];
}

export interface ScalabilityIssue {
  type: 'horizontal' | 'vertical' | 'data' | 'concurrent';
  description: string;
  currentLimit: number;
  projectedLimit: number;
  growthRate: number;
  timeToLimit: string;
  solutions: ScalabilitySolution[];
}

export interface ScalabilitySolution {
  approach: string;
  effort: 'small' | 'medium' | 'large';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  cost: 'low' | 'medium' | 'high';
}

export interface ResourceUsageAnalysis {
  cpu: ResourceMetrics;
  memory: ResourceMetrics;
  disk: ResourceMetrics;
  network: ResourceMetrics;
  database: ResourceMetrics;
}

export interface ResourceMetrics {
  current: number;
  peak: number;
  average: number;
  trend: 'increasing' | 'stable' | 'decreasing';
  efficiency: number;
  recommendations: string[];
}

export interface PerformanceOptimization {
  type: 'algorithm' | 'caching' | 'database' | 'network' | 'memory';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'small' | 'medium' | 'large';
  files: string[];
  implementation: OptimizationImplementation;
}

export interface OptimizationImplementation {
  steps: string[];
  tools: string[];
  testing: string[];
  monitoring: string[];
  rollback: string[];
}

export interface PerformanceTrend {
  metric: string;
  direction: 'improving' | 'stable' | 'degrading';
  rate: number;
  projection: TrendProjection;
}

export interface TrendProjection {
  shortTerm: number; // 1 month
  mediumTerm: number; // 3 months
  longTerm: number; // 6 months
  confidence: number;
}

export interface SecurityScanResult {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  vulnerabilities: SecurityVulnerabilityDetailed[];
  threats: SecurityThreat[];
  compliance: ComplianceAssessment[];
  recommendations: SecurityRecommendation[];
  trends: SecurityTrend[];
}

export interface SecurityVulnerabilityDetailed {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cvss: number;
  cwe: string;
  location: IssueLocation;
  description: string;
  exploitation: ExploitationAnalysis;
  remediation: RemediationPlan;
  references: string[];
}

export interface ExploitationAnalysis {
  difficulty: 'trivial' | 'easy' | 'medium' | 'hard' | 'expert';
  prerequisites: string[];
  impact: string;
  likelihood: number;
  attackVectors: string[];
}

export interface RemediationPlan {
  priority: 'low' | 'medium' | 'high' | 'urgent';
  effort: 'minimal' | 'small' | 'medium' | 'large';
  steps: string[];
  timeline: string;
  verification: string[];
}

export interface SecurityThreat {
  type: string;
  description: string;
  likelihood: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  mitigations: string[];
  monitoring: string[];
}

export interface ComplianceAssessment {
  standard: string;
  status: 'compliant' | 'partial' | 'non-compliant';
  score: number;
  gaps: ComplianceGap[];
  recommendations: string[];
}

export interface ComplianceGap {
  requirement: string;
  status: 'missing' | 'partial' | 'outdated';
  description: string;
  remediation: string;
  priority: 'low' | 'medium' | 'high';
}

export interface SecurityRecommendation {
  category: 'prevention' | 'detection' | 'response' | 'recovery';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  description: string;
  implementation: string[];
  tools: string[];
  timeline: string;
}

export interface SecurityTrend {
  metric: string;
  direction: 'improving' | 'stable' | 'degrading';
  timeframe: string;
  significance: 'low' | 'medium' | 'high';
}

export interface TechnicalDebtMeasurement {
  overallDebt: DebtMetrics;
  categories: DebtCategory[];
  hotspots: DebtHotspot[];
  trends: DebtTrend[];
  payoffStrategies: PayoffStrategy[];
}

export interface DebtMetrics {
  totalDebt: number;
  debtRatio: number;
  interestRate: number;
  payoffTime: string;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
}

export interface DebtCategory {
  type: 'code' | 'architecture' | 'test' | 'documentation' | 'infrastructure';
  amount: number;
  percentage: number;
  priority: 'low' | 'medium' | 'high';
  items: DebtItem[];
}

export interface DebtItem {
  description: string;
  location: IssueLocation;
  cost: number;
  interest: number;
  effort: 'small' | 'medium' | 'large';
  impact: 'low' | 'medium' | 'high';
}

export interface DebtHotspot {
  location: IssueLocation;
  debtAmount: number;
  complexity: number;
  changeFrequency: number;
  riskScore: number;
  recommendations: string[];
}

export interface DebtTrend {
  period: string;
  debtChange: number;
  newDebt: number;
  paidDebt: number;
  netChange: number;
  velocity: number;
}

export interface PayoffStrategy {
  name: string;
  description: string;
  targetDebt: DebtCategory[];
  timeline: string;
  effort: number;
  roi: number;
  risks: string[];
  milestones: PayoffMilestone[];
}

export interface PayoffMilestone {
  name: string;
  timeline: string;
  deliverables: string[];
  metrics: string[];
}

export interface PredictionSummary {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  criticalIssues: number;
  highPriorityActions: number;
  timeToAction: string;
  confidence: number;
  recommendations: string[];
  nextReview: string;
}
