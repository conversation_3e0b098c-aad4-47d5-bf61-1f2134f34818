/**
 * Prediction analysis engine - Orchestrate all predictive analysis capabilities
 */

import { CodebaseAnalysis } from "../analysis/types.js";
import { PredictionAnalysis, PredictionSummary } from "./types.js";
import { predictIssues } from "./issues.js";
import { assessPerformanceRisks } from "./performance.js";
import { scanSecurity } from "./security.js";
import { measureTechnicalDebt } from "./debt.js";

/**
 * Perform comprehensive prediction analysis
 */
export async function performPredictionAnalysis(analysis: CodebaseAnalysis): Promise<PredictionAnalysis> {
  console.log(`🔮 Starting comprehensive prediction analysis...`);
  
  // Run all prediction modules in parallel
  const [issues, performance, security, technicalDebt] = await Promise.all([
    predictIssues(analysis),
    assessPerformanceRisks(analysis),
    scanSecurity(analysis),
    measureTechnicalDebt(analysis)
  ]);
  
  // Generate summary
  const summary = generatePredictionSummary(issues, performance, security, technicalDebt);
  
  console.log(`🎯 Prediction analysis complete - Overall risk: ${summary.overallRisk}`);
  
  return {
    issues,
    performance,
    security,
    technicalDebt,
    summary
  };
}

/**
 * Generate prediction summary
 */
function generatePredictionSummary(
  issues: any[],
  performance: any,
  security: any,
  technicalDebt: any
): PredictionSummary {
  // Count critical issues
  const criticalIssues = issues.filter(issue => issue.severity === 'critical').length +
    (performance.overallRisk === 'critical' ? 1 : 0) +
    (security.overallRisk === 'critical' ? 1 : 0) +
    (technicalDebt.overallDebt.grade === 'F' ? 1 : 0);
  
  // Count high priority actions
  const highPriorityActions = issues.filter(issue => 
    issue.prevention.priority === 'high' || issue.prevention.priority === 'urgent'
  ).length +
    performance.optimizationOpportunities.filter((opt: any) => opt.impact === 'high').length +
    security.recommendations.filter((rec: any) => rec.priority === 'high' || rec.priority === 'urgent').length +
    technicalDebt.categories.filter((cat: any) => cat.priority === 'high').length;
  
  // Calculate overall risk
  const riskFactors = [
    mapRiskToScore(performance.overallRisk),
    mapRiskToScore(security.overallRisk),
    mapGradeToScore(technicalDebt.overallDebt.grade),
    Math.min(4, Math.ceil(issues.length / 5)) // Issue count factor
  ];
  
  const avgRiskScore = riskFactors.reduce((sum, score) => sum + score, 0) / riskFactors.length;
  const overallRisk = mapScoreToRisk(avgRiskScore);
  
  // Calculate confidence
  const confidenceFactors = issues.map((issue: any) => issue.confidence);
  const avgConfidence = confidenceFactors.length > 0 ? 
    confidenceFactors.reduce((sum: number, conf: number) => sum + conf, 0) / confidenceFactors.length : 0.7;
  
  // Determine time to action
  const urgentIssues = issues.filter((issue: any) => 
    issue.timeframe === 'immediate' || issue.prevention.priority === 'urgent'
  ).length;
  
  const timeToAction = urgentIssues > 0 ? 'immediate' :
    criticalIssues > 0 ? 'within 1 week' :
    highPriorityActions > 5 ? 'within 2 weeks' :
    'within 1 month';
  
  // Generate recommendations
  const recommendations = generateTopRecommendations(issues, performance, security, technicalDebt);
  
  // Calculate next review date
  const nextReview = overallRisk === 'critical' ? '1 week' :
    overallRisk === 'high' ? '2 weeks' :
    overallRisk === 'medium' ? '1 month' :
    '3 months';
  
  return {
    overallRisk,
    criticalIssues,
    highPriorityActions,
    timeToAction,
    confidence: Math.round(avgConfidence * 100) / 100,
    recommendations,
    nextReview
  };
}

/**
 * Generate top recommendations across all analysis areas
 */
function generateTopRecommendations(
  issues: any[],
  performance: any,
  security: any,
  technicalDebt: any
): string[] {
  const recommendations: Array<{ text: string; priority: number }> = [];
  
  // Critical issues first
  const criticalIssues = issues.filter(issue => issue.severity === 'critical');
  if (criticalIssues.length > 0) {
    recommendations.push({
      text: `Address ${criticalIssues.length} critical issues immediately`,
      priority: 10
    });
  }
  
  // Security vulnerabilities
  const criticalVulns = security.vulnerabilities.filter((v: any) => v.severity === 'critical');
  const highVulns = security.vulnerabilities.filter((v: any) => v.severity === 'high');
  if (criticalVulns.length > 0) {
    recommendations.push({
      text: `Fix ${criticalVulns.length} critical security vulnerabilities`,
      priority: 9
    });
  } else if (highVulns.length > 0) {
    recommendations.push({
      text: `Address ${highVulns.length} high-severity security vulnerabilities`,
      priority: 8
    });
  }
  
  // Performance bottlenecks
  const criticalBottlenecks = performance.bottlenecks.filter((b: any) => b.severity === 'critical');
  if (criticalBottlenecks.length > 0) {
    recommendations.push({
      text: `Resolve ${criticalBottlenecks.length} critical performance bottlenecks`,
      priority: 8
    });
  }
  
  // Technical debt
  if (technicalDebt.overallDebt.grade === 'F') {
    recommendations.push({
      text: `Implement debt reduction strategy - current grade: ${technicalDebt.overallDebt.grade}`,
      priority: 7
    });
  }
  
  // High-impact optimizations
  const highImpactOpts = performance.optimizationOpportunities.filter((opt: any) => opt.impact === 'high');
  if (highImpactOpts.length > 0) {
    recommendations.push({
      text: `Implement ${highImpactOpts.length} high-impact performance optimizations`,
      priority: 6
    });
  }
  
  // Architecture issues
  const architectureIssues = issues.filter(issue => issue.type === 'architecture-violation');
  if (architectureIssues.length > 0) {
    recommendations.push({
      text: `Fix ${architectureIssues.length} architecture principle violations`,
      priority: 6
    });
  }
  
  // Test coverage
  const testIssues = issues.filter(issue => issue.type === 'test-coverage-gap');
  if (testIssues.length > 0) {
    recommendations.push({
      text: 'Increase test coverage to reduce bug risk',
      priority: 5
    });
  }
  
  // Maintainability
  const maintainabilityIssues = issues.filter(issue => issue.type === 'maintainability-decline');
  if (maintainabilityIssues.length > 0) {
    recommendations.push({
      text: 'Improve code maintainability through refactoring',
      priority: 5
    });
  }
  
  // Dependency issues
  const dependencyIssues = issues.filter(issue => issue.type === 'dependency-conflict');
  if (dependencyIssues.length > 0) {
    recommendations.push({
      text: `Resolve ${dependencyIssues.length} dependency conflicts`,
      priority: 4
    });
  }
  
  // Security monitoring
  if (security.vulnerabilities.length > 0) {
    recommendations.push({
      text: 'Implement continuous security monitoring',
      priority: 4
    });
  }
  
  // Sort by priority and return top 10
  return recommendations
    .sort((a, b) => b.priority - a.priority)
    .slice(0, 10)
    .map(rec => rec.text);
}

/**
 * Map risk level to numeric score
 */
function mapRiskToScore(risk: 'low' | 'medium' | 'high' | 'critical'): number {
  switch (risk) {
    case 'low': return 1;
    case 'medium': return 2;
    case 'high': return 3;
    case 'critical': return 4;
    default: return 2;
  }
}

/**
 * Map grade to numeric score
 */
function mapGradeToScore(grade: 'A' | 'B' | 'C' | 'D' | 'F'): number {
  switch (grade) {
    case 'A': return 1;
    case 'B': return 1.5;
    case 'C': return 2;
    case 'D': return 3;
    case 'F': return 4;
    default: return 2;
  }
}

/**
 * Map numeric score back to risk level
 */
function mapScoreToRisk(score: number): 'low' | 'medium' | 'high' | 'critical' {
  if (score >= 3.5) return 'critical';
  if (score >= 2.5) return 'high';
  if (score >= 1.5) return 'medium';
  return 'low';
}

/**
 * Convenience function to run prediction analysis on a codebase
 */
export async function analyzePredictions(analysis: CodebaseAnalysis): Promise<PredictionAnalysis> {
  return await performPredictionAnalysis(analysis);
}
