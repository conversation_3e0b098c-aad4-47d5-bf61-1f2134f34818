/**
 * Technical debt measurement - Quantify and track technical debt
 */

import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import { 
  TechnicalDebtMeasurement,
  DebtMetrics,
  DebtCategory,
  DebtItem,
  DebtHotspot,
  DebtTrend,
  PayoffStrategy,
  PayoffMilestone,
  IssueLocation
} from "./types.js";

/**
 * Measure technical debt in the codebase
 */
export async function measureTechnicalDebt(analysis: CodebaseAnalysis): Promise<TechnicalDebtMeasurement> {
  console.log(`💳 Measuring technical debt...`);
  
  const categories = categorizeDebt(analysis);
  const overallDebt = calculateOverallDebt(categories);
  const hotspots = identifyDebtHotspots(analysis, categories);
  const trends = analyzeDebtTrends(analysis);
  const payoffStrategies = generatePayoffStrategies(categories, hotspots);
  
  console.log(`📊 Total debt: ${overallDebt.totalDebt} hours, Grade: ${overallDebt.grade}`);
  
  return {
    overallDebt,
    categories,
    hotspots,
    trends,
    payoffStrategies
  };
}

/**
 * Categorize different types of technical debt
 */
function categorizeDebt(analysis: CodebaseAnalysis): DebtCategory[] {
  const categories: DebtCategory[] = [];
  
  // Code debt
  const codeDebt = calculateCodeDebt(analysis);
  if (codeDebt.amount > 0) {
    categories.push(codeDebt);
  }
  
  // Architecture debt
  const architectureDebt = calculateArchitectureDebt(analysis);
  if (architectureDebt.amount > 0) {
    categories.push(architectureDebt);
  }
  
  // Test debt
  const testDebt = calculateTestDebt(analysis);
  if (testDebt.amount > 0) {
    categories.push(testDebt);
  }
  
  // Documentation debt
  const documentationDebt = calculateDocumentationDebt(analysis);
  if (documentationDebt.amount > 0) {
    categories.push(documentationDebt);
  }
  
  // Infrastructure debt
  const infrastructureDebt = calculateInfrastructureDebt(analysis);
  if (infrastructureDebt.amount > 0) {
    categories.push(infrastructureDebt);
  }
  
  // Calculate percentages
  const totalDebt = categories.reduce((sum, cat) => sum + cat.amount, 0);
  categories.forEach(cat => {
    cat.percentage = totalDebt > 0 ? Math.round((cat.amount / totalDebt) * 100) : 0;
  });
  
  return categories;
}

/**
 * Calculate code-related technical debt
 */
function calculateCodeDebt(analysis: CodebaseAnalysis): DebtCategory {
  const items: DebtItem[] = [];
  
  // High complexity functions
  for (const file of analysis.files) {
    for (const func of file.functions) {
      if (func.complexity > 15) {
        const cost = Math.ceil(func.complexity / 5) * 2; // 2 hours per 5 complexity points
        items.push({
          description: `High complexity function "${func.name}" (complexity: ${func.complexity})`,
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          cost,
          interest: cost * 0.1, // 10% interest per month
          effort: func.complexity > 25 ? 'large' : func.complexity > 20 ? 'medium' : 'small',
          impact: func.complexity > 25 ? 'high' : 'medium'
        });
      }
    }
    
    // Large classes
    for (const cls of file.classes) {
      if (cls.methods.length > 20) {
        const cost = Math.ceil(cls.methods.length / 5) * 3; // 3 hours per 5 methods
        items.push({
          description: `Large class "${cls.name}" (${cls.methods.length} methods)`,
          location: {
            file: file.path,
            class: cls.name,
            line: cls.startLine
          },
          cost,
          interest: cost * 0.08,
          effort: cls.methods.length > 30 ? 'large' : 'medium',
          impact: cls.methods.length > 30 ? 'high' : 'medium'
        });
      }
    }
    
    // Large files
    if (file.lines > 500) {
      const cost = Math.ceil(file.lines / 100) * 2; // 2 hours per 100 lines
      items.push({
        description: `Large file (${file.lines} lines)`,
        location: {
          file: file.path
        },
        cost,
        interest: cost * 0.05,
        effort: file.lines > 1000 ? 'large' : 'medium',
        impact: file.lines > 1000 ? 'high' : 'medium'
      });
    }
  }
  
  // Code duplication
  const avgDuplication = analysis.quality.maintainability.duplication;
  if (avgDuplication > 10) {
    const cost = Math.ceil(avgDuplication) * 2; // 2 hours per percentage point
    items.push({
      description: `Code duplication (${avgDuplication}%)`,
      location: {
        file: 'project-wide'
      },
      cost,
      interest: cost * 0.15, // High interest for duplication
      effort: avgDuplication > 20 ? 'large' : 'medium',
      impact: 'high'
    });
  }
  
  const totalAmount = items.reduce((sum, item) => sum + item.cost, 0);
  
  return {
    type: 'code',
    amount: totalAmount,
    percentage: 0, // Will be calculated later
    priority: totalAmount > 50 ? 'high' : totalAmount > 20 ? 'medium' : 'low',
    items
  };
}

/**
 * Calculate architecture-related technical debt
 */
function calculateArchitectureDebt(analysis: CodebaseAnalysis): DebtCategory {
  const items: DebtItem[] = [];
  
  // Circular dependencies
  for (const cycle of analysis.dependencies.cycles) {
    const cost = cycle.nodes.length * 4; // 4 hours per node in cycle
    items.push({
      description: `Circular dependency: ${cycle.nodes.join(' → ')}`,
      location: {
        file: cycle.nodes[0],
        module: cycle.nodes.join(', ')
      },
      cost,
      interest: cost * 0.12,
      effort: cycle.nodes.length > 4 ? 'large' : 'medium',
      impact: cycle.severity === 'high' ? 'high' : 'medium'
    });
  }
  
  // Architecture principle violations
  for (const violation of analysis.architecture.principles) {
    const cost = violation.severity === 'high' ? 8 : violation.severity === 'medium' ? 4 : 2;
    items.push({
      description: `${violation.principle} violation: ${violation.description}`,
      location: {
        file: violation.files[0] || 'project-wide'
      },
      cost,
      interest: cost * 0.1,
      effort: violation.severity === 'high' ? 'large' : 'medium',
      impact: violation.severity === 'high' ? 'high' : 'medium'
    });
  }
  
  // High coupling (many dependencies)
  if (analysis.dependencies.metrics.averageDependencies > 10) {
    const cost = Math.ceil(analysis.dependencies.metrics.averageDependencies) * 2;
    items.push({
      description: `High coupling (avg ${analysis.dependencies.metrics.averageDependencies} dependencies per file)`,
      location: {
        file: 'project-wide'
      },
      cost,
      interest: cost * 0.08,
      effort: 'large',
      impact: 'high'
    });
  }
  
  const totalAmount = items.reduce((sum, item) => sum + item.cost, 0);
  
  return {
    type: 'architecture',
    amount: totalAmount,
    percentage: 0,
    priority: totalAmount > 40 ? 'high' : totalAmount > 15 ? 'medium' : 'low',
    items
  };
}

/**
 * Calculate test-related technical debt
 */
function calculateTestDebt(analysis: CodebaseAnalysis): DebtCategory {
  const items: DebtItem[] = [];
  
  // Low test coverage
  const testCoverage = analysis.quality.reliability.testCoverage;
  if (testCoverage < 80) {
    const missingCoverage = 80 - testCoverage;
    const cost = Math.ceil(missingCoverage) * 1.5; // 1.5 hours per percentage point
    items.push({
      description: `Low test coverage (${testCoverage}%, missing ${missingCoverage}%)`,
      location: {
        file: 'project-wide'
      },
      cost,
      interest: cost * 0.2, // High interest for missing tests
      effort: missingCoverage > 30 ? 'large' : 'medium',
      impact: 'high'
    });
  }
  
  // Untested complex functions
  for (const file of analysis.files) {
    for (const func of file.functions) {
      if (func.complexity > 10) {
        // Assume complex functions need more testing
        const cost = Math.ceil(func.complexity / 5) * 1; // 1 hour per 5 complexity points
        items.push({
          description: `Complex function "${func.name}" needs comprehensive testing`,
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          cost,
          interest: cost * 0.15,
          effort: 'small',
          impact: 'medium'
        });
      }
    }
  }
  
  const totalAmount = items.reduce((sum, item) => sum + item.cost, 0);
  
  return {
    type: 'test',
    amount: totalAmount,
    percentage: 0,
    priority: totalAmount > 30 ? 'high' : totalAmount > 10 ? 'medium' : 'low',
    items
  };
}

/**
 * Calculate documentation-related technical debt
 */
function calculateDocumentationDebt(analysis: CodebaseAnalysis): DebtCategory {
  const items: DebtItem[] = [];
  
  // Low documentation coverage
  const docCoverage = analysis.quality.maintainability.documentation;
  if (docCoverage < 50) {
    const missingDoc = 50 - docCoverage;
    const cost = Math.ceil(missingDoc) * 0.5; // 0.5 hours per percentage point
    items.push({
      description: `Low documentation coverage (${docCoverage}%, missing ${missingDoc}%)`,
      location: {
        file: 'project-wide'
      },
      cost,
      interest: cost * 0.05,
      effort: missingDoc > 30 ? 'large' : 'medium',
      impact: 'medium'
    });
  }
  
  // Undocumented complex functions
  for (const file of analysis.files) {
    for (const func of file.functions) {
      if (func.complexity > 10 && !func.documentation) {
        const cost = Math.ceil(func.complexity / 10) * 1; // 1 hour per 10 complexity points
        items.push({
          description: `Undocumented complex function "${func.name}"`,
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          cost,
          interest: cost * 0.03,
          effort: 'small',
          impact: 'low'
        });
      }
    }
  }
  
  const totalAmount = items.reduce((sum, item) => sum + item.cost, 0);
  
  return {
    type: 'documentation',
    amount: totalAmount,
    percentage: 0,
    priority: totalAmount > 20 ? 'medium' : 'low',
    items
  };
}

/**
 * Calculate infrastructure-related technical debt
 */
function calculateInfrastructureDebt(analysis: CodebaseAnalysis): DebtCategory {
  const items: DebtItem[] = [];
  
  // Outdated dependencies (simplified check)
  if (analysis.dependencies.metrics.totalDependencies > 100) {
    const cost = Math.ceil(analysis.dependencies.metrics.totalDependencies / 10) * 2;
    items.push({
      description: `Large number of dependencies (${analysis.dependencies.metrics.totalDependencies}) may include outdated packages`,
      location: {
        file: 'package.json'
      },
      cost,
      interest: cost * 0.1,
      effort: 'medium',
      impact: 'medium'
    });
  }
  
  const totalAmount = items.reduce((sum, item) => sum + item.cost, 0);
  
  return {
    type: 'infrastructure',
    amount: totalAmount,
    percentage: 0,
    priority: totalAmount > 15 ? 'medium' : 'low',
    items
  };
}

/**
 * Calculate overall debt metrics
 */
function calculateOverallDebt(categories: DebtCategory[]): DebtMetrics {
  const totalDebt = categories.reduce((sum, cat) => sum + cat.amount, 0);
  const totalInterest = categories.reduce((sum, cat) => 
    sum + cat.items.reduce((itemSum, item) => itemSum + item.interest, 0), 0
  );
  
  // Calculate debt ratio (debt / total project size estimate)
  const estimatedProjectSize = totalDebt * 2; // Assume debt is 50% of project
  const debtRatio = totalDebt / (totalDebt + estimatedProjectSize);
  
  // Calculate interest rate (monthly)
  const interestRate = totalDebt > 0 ? totalInterest / totalDebt : 0;
  
  // Calculate payoff time (simplified)
  const payoffTime = totalDebt > 0 ? `${Math.ceil(totalDebt / 40)} weeks` : '0 weeks'; // 40 hours per week
  
  // Calculate grade
  let grade: 'A' | 'B' | 'C' | 'D' | 'F';
  if (totalDebt < 20) grade = 'A';
  else if (totalDebt < 50) grade = 'B';
  else if (totalDebt < 100) grade = 'C';
  else if (totalDebt < 200) grade = 'D';
  else grade = 'F';
  
  return {
    totalDebt: Math.round(totalDebt),
    debtRatio: Math.round(debtRatio * 100) / 100,
    interestRate: Math.round(interestRate * 100) / 100,
    payoffTime,
    grade
  };
}

/**
 * Identify debt hotspots
 */
function identifyDebtHotspots(analysis: CodebaseAnalysis, categories: DebtCategory[]): DebtHotspot[] {
  const hotspots: DebtHotspot[] = [];
  
  // Group debt items by file
  const fileDebtMap = new Map<string, DebtItem[]>();
  
  for (const category of categories) {
    for (const item of category.items) {
      const file = item.location.file;
      if (!fileDebtMap.has(file)) {
        fileDebtMap.set(file, []);
      }
      fileDebtMap.get(file)!.push(item);
    }
  }
  
  // Calculate hotspot metrics for each file
  for (const [filePath, items] of fileDebtMap) {
    const fileAnalysis = analysis.files.find(f => f.path === filePath);
    if (!fileAnalysis) continue;
    
    const debtAmount = items.reduce((sum, item) => sum + item.cost, 0);
    const complexity = fileAnalysis.complexity.cyclomatic;
    const changeFrequency = 0.5; // Simplified - would need git history
    
    // Calculate risk score
    const riskScore = (debtAmount * 0.4) + (complexity * 0.3) + (changeFrequency * 100 * 0.3);
    
    if (debtAmount > 10) { // Only include significant debt
      hotspots.push({
        location: {
          file: filePath
        },
        debtAmount,
        complexity,
        changeFrequency,
        riskScore: Math.round(riskScore),
        recommendations: [
          `Prioritize refactoring (${debtAmount} hours of debt)`,
          complexity > 15 ? 'Reduce complexity' : 'Maintain current complexity',
          'Add comprehensive tests',
          'Improve documentation'
        ]
      });
    }
  }
  
  // Sort by risk score
  hotspots.sort((a, b) => b.riskScore - a.riskScore);
  
  return hotspots.slice(0, 10); // Top 10 hotspots
}

/**
 * Analyze debt trends
 */
function analyzeDebtTrends(analysis: CodebaseAnalysis): DebtTrend[] {
  // Simplified trend analysis - would need historical data
  return [
    {
      period: 'Last Month',
      debtChange: 5,
      newDebt: 15,
      paidDebt: 10,
      netChange: 5,
      velocity: 10 // Hours of debt paid per week
    },
    {
      period: 'Last Quarter',
      debtChange: 20,
      newDebt: 50,
      paidDebt: 30,
      netChange: 20,
      velocity: 8
    }
  ];
}

/**
 * Generate debt payoff strategies
 */
function generatePayoffStrategies(categories: DebtCategory[], hotspots: DebtHotspot[]): PayoffStrategy[] {
  const strategies: PayoffStrategy[] = [];
  
  // High-impact strategy
  const highImpactDebt = categories.filter(cat => cat.priority === 'high');
  if (highImpactDebt.length > 0) {
    strategies.push({
      name: 'High-Impact Debt Reduction',
      description: 'Focus on high-priority debt items that provide maximum benefit',
      targetDebt: highImpactDebt,
      timeline: '6-8 weeks',
      effort: highImpactDebt.reduce((sum, cat) => sum + cat.amount, 0),
      roi: 3.5, // 3.5x return on investment
      risks: [
        'May require significant refactoring',
        'Potential for introducing new bugs',
        'Resource intensive'
      ],
      milestones: [
        {
          name: 'Architecture Debt Resolution',
          timeline: '2 weeks',
          deliverables: ['Circular dependency fixes', 'Principle violation fixes'],
          metrics: ['Dependency cycles reduced to 0', 'High-severity violations < 2']
        },
        {
          name: 'Code Quality Improvement',
          timeline: '4 weeks',
          deliverables: ['Complex function refactoring', 'Code duplication removal'],
          metrics: ['Average complexity < 15', 'Duplication < 10%']
        }
      ]
    });
  }
  
  // Hotspot-focused strategy
  if (hotspots.length > 0) {
    strategies.push({
      name: 'Hotspot-Focused Approach',
      description: 'Target the highest-risk debt hotspots first',
      targetDebt: categories,
      timeline: '4-6 weeks',
      effort: hotspots.slice(0, 5).reduce((sum, hotspot) => sum + hotspot.debtAmount, 0),
      roi: 2.8,
      risks: [
        'May not address systemic issues',
        'Requires careful prioritization'
      ],
      milestones: [
        {
          name: 'Top 3 Hotspots',
          timeline: '3 weeks',
          deliverables: ['Refactor highest-risk files', 'Add comprehensive tests'],
          metrics: ['Risk score reduction > 50%', 'Test coverage > 80%']
        }
      ]
    });
  }
  
  // Incremental strategy
  strategies.push({
    name: 'Incremental Improvement',
    description: 'Gradual debt reduction with minimal disruption',
    targetDebt: categories,
    timeline: '12-16 weeks',
    effort: categories.reduce((sum, cat) => sum + cat.amount, 0),
    roi: 2.0,
    risks: [
      'Slower progress',
      'May not address urgent issues quickly'
    ],
    milestones: [
      {
        name: 'Documentation Improvement',
        timeline: '2 weeks',
        deliverables: ['Function documentation', 'API documentation'],
        metrics: ['Documentation coverage > 70%']
      },
      {
        name: 'Test Coverage Increase',
        timeline: '4 weeks',
        deliverables: ['Unit tests', 'Integration tests'],
        metrics: ['Test coverage > 80%']
      },
      {
        name: 'Code Quality Enhancement',
        timeline: '8 weeks',
        deliverables: ['Refactored complex functions', 'Reduced duplication'],
        metrics: ['Complexity < 15', 'Duplication < 5%']
      }
    ]
  });
  
  return strategies;
}
