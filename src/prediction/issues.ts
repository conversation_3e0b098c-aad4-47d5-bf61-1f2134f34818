/**
 * Issue prediction engine - Predict potential problems before they occur
 */

import { 
  CodebaseAnalysis, 
  FileAnalysis, 
  FunctionMetadata, 
  ClassMetadata 
} from "../analysis/types.js";
import { 
  IssuePrediction, 
  IssueType, 
  IssueLocation, 
  IssueImpact, 
  PreventionStrategy,
  PreventionAction
} from "./types.js";

/**
 * Predict potential issues in the codebase
 */
export async function predictIssues(analysis: CodebaseAnalysis): Promise<IssuePrediction[]> {
  console.log(`🔮 Predicting potential issues...`);
  
  const predictions: IssuePrediction[] = [];
  
  // Predict different types of issues
  predictions.push(...predictBugProneCode(analysis));
  predictions.push(...predictPerformanceDegradation(analysis));
  predictions.push(...predictMaintainabilityDecline(analysis));
  predictions.push(...predictScalabilityBottlenecks(analysis));
  predictions.push(...predictDependencyConflicts(analysis));
  predictions.push(...predictTechnicalDebtAccumulation(analysis));
  predictions.push(...predictTestCoverageGaps(analysis));
  predictions.push(...predictArchitectureViolations(analysis));
  
  // Sort by severity and confidence
  predictions.sort((a, b) => {
    const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
    if (severityDiff !== 0) return severityDiff;
    return b.confidence - a.confidence;
  });
  
  console.log(`🎯 Predicted ${predictions.length} potential issues`);
  return predictions;
}

/**
 * Predict bug-prone code based on complexity and patterns
 */
function predictBugProneCode(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  for (const file of analysis.files) {
    // High complexity functions are bug-prone
    for (const func of file.functions) {
      if (func.complexity > 15) {
        predictions.push({
          id: `bug-prone-${file.path}-${func.name}`,
          type: 'bug-prone-code',
          severity: func.complexity > 25 ? 'high' : 'medium',
          confidence: Math.min(0.9, func.complexity / 30),
          probability: Math.min(0.8, func.complexity / 25),
          timeframe: 'short-term',
          description: `Function "${func.name}" has high complexity (${func.complexity}) making it prone to bugs`,
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          impact: {
            scope: 'local',
            affectedFiles: [file.path],
            userImpact: func.complexity > 25 ? 'moderate' : 'minor',
            businessImpact: 'low',
            technicalImpact: 'medium'
          },
          prevention: {
            actions: [{
              type: 'refactor',
              description: 'Break down complex function into smaller, focused functions',
              steps: [
                'Identify distinct responsibilities within the function',
                'Extract logical blocks into separate functions',
                'Add unit tests for each extracted function',
                'Verify original functionality is preserved'
              ],
              tools: ['IDE refactoring tools', 'Unit testing framework'],
              estimatedHours: Math.ceil(func.complexity / 5)
            }],
            priority: func.complexity > 25 ? 'high' : 'medium',
            effort: func.complexity > 25 ? 'medium' : 'small',
            timeline: func.complexity > 25 ? '1-2 weeks' : '3-5 days',
            resources: ['Senior developer', 'Code reviewer']
          },
          relatedIssues: []
        });
      }
    }
    
    // Large classes are also bug-prone
    for (const cls of file.classes) {
      if (cls.methods.length > 20) {
        predictions.push({
          id: `bug-prone-class-${file.path}-${cls.name}`,
          type: 'bug-prone-code',
          severity: cls.methods.length > 30 ? 'high' : 'medium',
          confidence: Math.min(0.8, cls.methods.length / 40),
          probability: Math.min(0.7, cls.methods.length / 35),
          timeframe: 'medium-term',
          description: `Class "${cls.name}" has too many methods (${cls.methods.length}) violating SRP`,
          location: {
            file: file.path,
            class: cls.name,
            line: cls.startLine
          },
          impact: {
            scope: 'module',
            affectedFiles: [file.path],
            userImpact: 'minor',
            businessImpact: 'low',
            technicalImpact: 'medium'
          },
          prevention: {
            actions: [{
              type: 'refactor',
              description: 'Split large class into smaller, focused classes',
              steps: [
                'Identify cohesive groups of methods',
                'Extract related methods into new classes',
                'Update dependencies and imports',
                'Add tests for new class structure'
              ],
              tools: ['IDE refactoring tools', 'Dependency analysis tools'],
              estimatedHours: Math.ceil(cls.methods.length / 3)
            }],
            priority: cls.methods.length > 30 ? 'high' : 'medium',
            effort: cls.methods.length > 30 ? 'large' : 'medium',
            timeline: cls.methods.length > 30 ? '2-3 weeks' : '1-2 weeks',
            resources: ['Senior developer', 'Architect', 'Code reviewer']
          },
          relatedIssues: []
        });
      }
    }
  }
  
  return predictions;
}

/**
 * Predict performance degradation based on algorithmic complexity
 */
function predictPerformanceDegradation(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // Look for nested loops and high complexity algorithms
  for (const file of analysis.files) {
    for (const func of file.functions) {
      // Predict O(n²) or worse complexity
      if (func.complexity > 20 && func.calls.some(call => 
        call.includes('forEach') || call.includes('map') || call.includes('filter')
      )) {
        predictions.push({
          id: `perf-degradation-${file.path}-${func.name}`,
          type: 'performance-degradation',
          severity: 'medium',
          confidence: 0.6,
          probability: 0.7,
          timeframe: 'medium-term',
          description: `Function "${func.name}" may have poor algorithmic complexity with nested iterations`,
          location: {
            file: file.path,
            function: func.name,
            line: func.startLine
          },
          impact: {
            scope: 'system',
            affectedFiles: [file.path],
            userImpact: 'moderate',
            businessImpact: 'medium',
            technicalImpact: 'high'
          },
          prevention: {
            actions: [{
              type: 'refactor',
              description: 'Optimize algorithm to reduce time complexity',
              steps: [
                'Profile function performance with large datasets',
                'Identify bottlenecks in nested iterations',
                'Implement more efficient algorithms or data structures',
                'Add performance tests to prevent regression'
              ],
              tools: ['Profiling tools', 'Performance testing framework'],
              estimatedHours: 8
            }],
            priority: 'medium',
            effort: 'medium',
            timeline: '1 week',
            resources: ['Senior developer', 'Performance engineer']
          },
          relatedIssues: []
        });
      }
    }
  }
  
  return predictions;
}

/**
 * Predict maintainability decline based on code quality metrics
 */
function predictMaintainabilityDecline(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // Low maintainability index
  if (analysis.quality.maintainability.index < 50) {
    predictions.push({
      id: 'maintainability-decline-overall',
      type: 'maintainability-decline',
      severity: analysis.quality.maintainability.index < 30 ? 'high' : 'medium',
      confidence: 0.8,
      probability: 0.9,
      timeframe: 'short-term',
      description: `Overall maintainability index is low (${analysis.quality.maintainability.index}/100)`,
      location: {
        file: 'project-wide',
        module: 'all'
      },
      impact: {
        scope: 'global',
        affectedFiles: analysis.files.map(f => f.path),
        userImpact: 'minor',
        businessImpact: 'high',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [
          {
            type: 'refactor',
            description: 'Systematic refactoring to improve maintainability',
            steps: [
              'Identify files with lowest maintainability scores',
              'Prioritize refactoring based on change frequency',
              'Implement coding standards and guidelines',
              'Set up automated quality gates'
            ],
            tools: ['Static analysis tools', 'Code quality metrics'],
            estimatedHours: 40
          },
          {
            type: 'document',
            description: 'Improve code documentation',
            steps: [
              'Add missing function and class documentation',
              'Create architectural decision records',
              'Update README and developer guides',
              'Establish documentation standards'
            ],
            tools: ['Documentation generators', 'Wiki systems'],
            estimatedHours: 20
          }
        ],
        priority: 'high',
        effort: 'large',
        timeline: '4-6 weeks',
        resources: ['Development team', 'Technical lead', 'Documentation specialist']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}

/**
 * Predict scalability bottlenecks
 */
function predictScalabilityBottlenecks(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // Large files that might become bottlenecks
  const largeFiles = analysis.files.filter(f => f.lines > 1000);
  for (const file of largeFiles) {
    predictions.push({
      id: `scalability-bottleneck-${file.path}`,
      type: 'scalability-bottleneck',
      severity: file.lines > 2000 ? 'high' : 'medium',
      confidence: 0.7,
      probability: 0.6,
      timeframe: 'long-term',
      description: `Large file (${file.lines} lines) may become a scalability bottleneck`,
      location: {
        file: file.path
      },
      impact: {
        scope: 'module',
        affectedFiles: [file.path],
        userImpact: 'minor',
        businessImpact: 'medium',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [{
          type: 'refactor',
          description: 'Split large file into smaller, focused modules',
          steps: [
            'Analyze file structure and identify logical boundaries',
            'Extract related functionality into separate files',
            'Update imports and dependencies',
            'Ensure proper module interfaces'
          ],
          tools: ['IDE refactoring tools', 'Module bundlers'],
          estimatedHours: Math.ceil(file.lines / 100)
        }],
        priority: file.lines > 2000 ? 'high' : 'medium',
        effort: file.lines > 2000 ? 'large' : 'medium',
        timeline: file.lines > 2000 ? '2-3 weeks' : '1-2 weeks',
        resources: ['Senior developer', 'Architect']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}

/**
 * Predict dependency conflicts
 */
function predictDependencyConflicts(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // Circular dependencies are conflict-prone
  for (const cycle of analysis.dependencies.cycles) {
    predictions.push({
      id: `dependency-conflict-${cycle.nodes.join('-')}`,
      type: 'dependency-conflict',
      severity: cycle.severity === 'high' ? 'high' : 'medium',
      confidence: 0.9,
      probability: 0.8,
      timeframe: 'short-term',
      description: `Circular dependency detected: ${cycle.nodes.join(' → ')}`,
      location: {
        file: cycle.nodes[0],
        module: cycle.nodes.join(', ')
      },
      impact: {
        scope: 'system',
        affectedFiles: cycle.nodes,
        userImpact: 'moderate',
        businessImpact: 'medium',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [{
          type: 'refactor',
          description: 'Break circular dependency',
          steps: [
            'Analyze dependency chain to find break point',
            'Extract common functionality to shared module',
            'Use dependency injection or event patterns',
            'Verify no circular dependencies remain'
          ],
          tools: ['Dependency analysis tools', 'Refactoring tools'],
          estimatedHours: cycle.nodes.length * 4
        }],
        priority: 'high',
        effort: 'medium',
        timeline: '1-2 weeks',
        resources: ['Senior developer', 'Architect']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}

/**
 * Predict technical debt accumulation
 */
function predictTechnicalDebtAccumulation(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // High duplication leads to debt
  if (analysis.quality.maintainability.duplication > 15) {
    predictions.push({
      id: 'tech-debt-duplication',
      type: 'technical-debt-accumulation',
      severity: 'medium',
      confidence: 0.8,
      probability: 0.9,
      timeframe: 'medium-term',
      description: `High code duplication (${analysis.quality.maintainability.duplication}%) will accumulate technical debt`,
      location: {
        file: 'project-wide'
      },
      impact: {
        scope: 'global',
        affectedFiles: analysis.files.map(f => f.path),
        userImpact: 'minor',
        businessImpact: 'high',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [{
          type: 'refactor',
          description: 'Eliminate code duplication',
          steps: [
            'Identify duplicated code blocks',
            'Extract common functionality to shared utilities',
            'Update all usage sites',
            'Add tests to prevent regression'
          ],
          tools: ['Duplication detection tools', 'Refactoring tools'],
          estimatedHours: 24
        }],
        priority: 'medium',
        effort: 'medium',
        timeline: '2-3 weeks',
        resources: ['Development team', 'Code reviewer']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}

/**
 * Predict test coverage gaps
 */
function predictTestCoverageGaps(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // Low test coverage prediction
  if (analysis.quality.reliability.testCoverage < 70) {
    predictions.push({
      id: 'test-coverage-gap',
      type: 'test-coverage-gap',
      severity: analysis.quality.reliability.testCoverage < 50 ? 'high' : 'medium',
      confidence: 0.9,
      probability: 0.95,
      timeframe: 'immediate',
      description: `Low test coverage (${analysis.quality.reliability.testCoverage}%) increases bug risk`,
      location: {
        file: 'project-wide'
      },
      impact: {
        scope: 'global',
        affectedFiles: analysis.files.map(f => f.path),
        userImpact: 'major',
        businessImpact: 'high',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [{
          type: 'test',
          description: 'Increase test coverage',
          steps: [
            'Identify untested code paths',
            'Write unit tests for critical functions',
            'Add integration tests for key workflows',
            'Set up coverage monitoring and gates'
          ],
          tools: ['Testing frameworks', 'Coverage tools'],
          estimatedHours: 32
        }],
        priority: 'high',
        effort: 'large',
        timeline: '3-4 weeks',
        resources: ['QA engineer', 'Developers', 'Test automation specialist']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}

/**
 * Predict architecture violations
 */
function predictArchitectureViolations(analysis: CodebaseAnalysis): IssuePrediction[] {
  const predictions: IssuePrediction[] = [];
  
  // High severity principle violations
  const highViolations = analysis.architecture.principles.filter(p => p.severity === 'high');
  if (highViolations.length > 0) {
    predictions.push({
      id: 'architecture-violations',
      type: 'architecture-violation',
      severity: 'high',
      confidence: 0.9,
      probability: 0.8,
      timeframe: 'short-term',
      description: `${highViolations.length} high-severity architecture principle violations detected`,
      location: {
        file: 'project-wide'
      },
      impact: {
        scope: 'global',
        affectedFiles: highViolations.flatMap(v => v.files),
        userImpact: 'moderate',
        businessImpact: 'high',
        technicalImpact: 'high'
      },
      prevention: {
        actions: [{
          type: 'refactor',
          description: 'Address architecture principle violations',
          steps: [
            'Review each violation and its impact',
            'Prioritize fixes based on business impact',
            'Implement architectural improvements',
            'Establish architecture governance'
          ],
          tools: ['Architecture analysis tools', 'Design patterns'],
          estimatedHours: highViolations.length * 8
        }],
        priority: 'high',
        effort: 'large',
        timeline: '4-6 weeks',
        resources: ['Architect', 'Senior developers', 'Technical lead']
      },
      relatedIssues: []
    });
  }
  
  return predictions;
}
