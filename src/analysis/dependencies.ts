/**
 * Dependency analysis - Analyze module dependencies and relationships
 */

import * as path from "path";
import { 
  FileAnalysis, 
  DependencyGraph, 
  DependencyNode, 
  DependencyEdge, 
  DependencyCycle,
  DependencyMetrics 
} from "./types.js";

/**
 * Analyze dependencies across all files
 */
export async function analyzeDependencies(files: FileAnalysis[]): Promise<DependencyGraph> {
  console.log(`🔗 Analyzing dependencies for ${files.length} files...`);
  
  const nodes = createDependencyNodes(files);
  const edges = createDependencyEdges(files, nodes);
  const cycles = detectCircularDependencies(nodes, edges);
  const metrics = calculateDependencyMetrics(nodes, edges, cycles);
  
  console.log(`📊 Found ${edges.length} dependencies, ${cycles.length} circular dependencies`);
  
  return {
    nodes,
    edges,
    cycles,
    metrics
  };
}

/**
 * Create dependency nodes from file analyses
 */
function createDependencyNodes(files: FileAnalysis[]): DependencyNode[] {
  return files.map(file => ({
    id: normalizeModulePath(file.path),
    name: path.basename(file.path, path.extname(file.path)),
    type: 'file' as const,
    path: file.path,
    size: file.size,
    complexity: file.complexity.cyclomatic
  }));
}

/**
 * Create dependency edges from import relationships
 */
function createDependencyEdges(files: FileAnalysis[], nodes: DependencyNode[]): DependencyEdge[] {
  const edges: DependencyEdge[] = [];
  const nodeMap = new Map(nodes.map(node => [node.path, node]));
  
  for (const file of files) {
    const sourceId = normalizeModulePath(file.path);
    
    // Process imports
    for (const importDecl of file.imports) {
      const targetPath = resolveImportPath(file.path, importDecl.source);
      const targetNode = findNodeByPath(nodes, targetPath);
      
      if (targetNode) {
        edges.push({
          source: sourceId,
          target: targetNode.id,
          type: 'import',
          strength: calculateImportStrength(importDecl.imports),
          line: importDecl.line
        });
      }
    }
    
    // Process function calls (simplified)
    for (const func of file.functions) {
      for (const call of func.calls) {
        // Try to resolve function calls to modules
        const targetPath = resolveFunctionCall(file.path, call);
        const targetNode = findNodeByPath(nodes, targetPath);
        
        if (targetNode && targetNode.id !== sourceId) {
          edges.push({
            source: sourceId,
            target: targetNode.id,
            type: 'call',
            strength: 0.5
          });
        }
      }
    }
    
    // Process inheritance relationships
    for (const cls of file.classes) {
      if (cls.extends) {
        const targetPath = resolveTypePath(file.path, cls.extends);
        const targetNode = findNodeByPath(nodes, targetPath);
        
        if (targetNode) {
          edges.push({
            source: sourceId,
            target: targetNode.id,
            type: 'inheritance',
            strength: 1.0
          });
        }
      }
      
      for (const impl of cls.implements) {
        const targetPath = resolveTypePath(file.path, impl);
        const targetNode = findNodeByPath(nodes, targetPath);
        
        if (targetNode) {
          edges.push({
            source: sourceId,
            target: targetNode.id,
            type: 'composition',
            strength: 0.8
          });
        }
      }
    }
  }
  
  return edges;
}

/**
 * Detect circular dependencies using DFS
 */
function detectCircularDependencies(
  nodes: DependencyNode[], 
  edges: DependencyEdge[]
): DependencyCycle[] {
  const cycles: DependencyCycle[] = [];
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  const adjacencyList = buildAdjacencyList(edges);
  
  function dfs(nodeId: string, path: string[]): void {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    path.push(nodeId);
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        dfs(neighbor, [...path]);
      } else if (recursionStack.has(neighbor)) {
        // Found a cycle
        const cycleStart = path.indexOf(neighbor);
        const cycleNodes = path.slice(cycleStart);
        cycleNodes.push(neighbor); // Complete the cycle
        
        cycles.push({
          nodes: cycleNodes,
          severity: calculateCycleSeverity(cycleNodes, edges),
          impact: calculateCycleImpact(cycleNodes, nodes)
        });
      }
    }
    
    recursionStack.delete(nodeId);
  }
  
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      dfs(node.id, []);
    }
  }
  
  return cycles;
}

/**
 * Calculate dependency metrics
 */
function calculateDependencyMetrics(
  nodes: DependencyNode[], 
  edges: DependencyEdge[], 
  cycles: DependencyCycle[]
): DependencyMetrics {
  const inDegree = new Map<string, number>();
  const outDegree = new Map<string, number>();
  
  // Initialize degrees
  for (const node of nodes) {
    inDegree.set(node.id, 0);
    outDegree.set(node.id, 0);
  }
  
  // Calculate degrees
  for (const edge of edges) {
    outDegree.set(edge.source, (outDegree.get(edge.source) || 0) + 1);
    inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
  }
  
  const totalDependencies = edges.length;
  const averageDependencies = totalDependencies / nodes.length;
  const maxDepth = calculateMaxDependencyDepth(nodes, edges);
  
  // Calculate instability (Ce / (Ca + Ce))
  // Ce = Efferent coupling (outgoing dependencies)
  // Ca = Afferent coupling (incoming dependencies)
  let totalInstability = 0;
  let validNodes = 0;
  
  for (const node of nodes) {
    const ce = outDegree.get(node.id) || 0;
    const ca = inDegree.get(node.id) || 0;
    
    if (ca + ce > 0) {
      totalInstability += ce / (ca + ce);
      validNodes++;
    }
  }
  
  const instability = validNodes > 0 ? totalInstability / validNodes : 0;
  
  // Calculate abstractness (simplified - would need more analysis)
  const abstractness = 0.5; // Placeholder
  
  return {
    totalDependencies,
    circularDependencies: cycles.length,
    maxDepth,
    averageDependencies: Math.round(averageDependencies * 100) / 100,
    instability: Math.round(instability * 100) / 100,
    abstractness: Math.round(abstractness * 100) / 100
  };
}

/**
 * Helper functions
 */

function normalizeModulePath(filePath: string): string {
  return filePath.replace(/\\/g, '/');
}

function resolveImportPath(currentFile: string, importSource: string): string {
  if (importSource.startsWith('.')) {
    // Relative import
    const currentDir = path.dirname(currentFile);
    return path.normalize(path.join(currentDir, importSource));
  } else {
    // Absolute import or node_modules
    return importSource;
  }
}

function resolveFunctionCall(currentFile: string, call: string): string {
  // Simplified resolution - in reality, this would be much more complex
  return currentFile;
}

function resolveTypePath(currentFile: string, typeName: string): string {
  // Simplified type resolution
  return currentFile;
}

function findNodeByPath(nodes: DependencyNode[], targetPath: string): DependencyNode | undefined {
  const normalizedTarget = normalizeModulePath(targetPath);
  return nodes.find(node => 
    node.path === normalizedTarget || 
    node.path.endsWith(normalizedTarget) ||
    normalizedTarget.endsWith(node.path)
  );
}

function calculateImportStrength(imports: string[]): number {
  // More imports = stronger dependency
  return Math.min(1.0, imports.length * 0.2);
}

function buildAdjacencyList(edges: DependencyEdge[]): Map<string, string[]> {
  const adjacencyList = new Map<string, string[]>();
  
  for (const edge of edges) {
    const neighbors = adjacencyList.get(edge.source) || [];
    neighbors.push(edge.target);
    adjacencyList.set(edge.source, neighbors);
  }
  
  return adjacencyList;
}

function calculateCycleSeverity(cycleNodes: string[], edges: DependencyEdge[]): 'low' | 'medium' | 'high' {
  const cycleLength = cycleNodes.length - 1; // Subtract 1 because last node repeats first
  
  if (cycleLength <= 2) return 'low';
  if (cycleLength <= 4) return 'medium';
  return 'high';
}

function calculateCycleImpact(cycleNodes: string[], nodes: DependencyNode[]): number {
  // Calculate impact based on complexity and size of involved nodes
  let totalComplexity = 0;
  let totalSize = 0;
  
  for (const nodeId of cycleNodes) {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      totalComplexity += node.complexity;
      totalSize += node.size;
    }
  }
  
  return Math.round((totalComplexity + totalSize / 1000) * 100) / 100;
}

function calculateMaxDependencyDepth(nodes: DependencyNode[], edges: DependencyEdge[]): number {
  const adjacencyList = buildAdjacencyList(edges);
  let maxDepth = 0;
  
  function dfs(nodeId: string, depth: number, visited: Set<string>): number {
    if (visited.has(nodeId)) return depth;
    
    visited.add(nodeId);
    let currentMaxDepth = depth;
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      const neighborDepth = dfs(neighbor, depth + 1, new Set(visited));
      currentMaxDepth = Math.max(currentMaxDepth, neighborDepth);
    }
    
    return currentMaxDepth;
  }
  
  for (const node of nodes) {
    const depth = dfs(node.id, 0, new Set());
    maxDepth = Math.max(maxDepth, depth);
  }
  
  return maxDepth;
}
