/**
 * Architecture analysis - Analyze architectural patterns and principles
 */

import * as path from "path";
import {
  FileAnalysis,
  DependencyGraph,
  ArchitectureInsights,
  ArchitecturePattern,
  PrincipleViolation,
  ArchitectureSuggestion,
  LayerAnalysis,
  Layer,
  LayerViolation
} from "./types.js";

/**
 * Analyze architecture patterns and principles
 */
export async function analyzeArchitecture(
  files: FileAnalysis[],
  dependencies: DependencyGraph
): Promise<ArchitectureInsights> {
  console.log(`🏗️ Analyzing architecture for ${files.length} files...`);

  const patterns = detectArchitecturePatterns(files, dependencies);
  const principles = checkDesignPrinciples(files, dependencies);
  const suggestions = generateArchitectureSuggestions(files, dependencies, principles);
  const layering = analyzeLayering(files, dependencies);

  console.log(`📐 Found ${patterns.length} patterns, ${principles.length} principle violations`);

  return {
    patterns,
    principles,
    suggestions,
    layering
  };
}

/**
 * Detect common architecture patterns
 */
function detectArchitecturePatterns(
  files: FileAnalysis[],
  dependencies: DependencyGraph
): ArchitecturePattern[] {
  const patterns: ArchitecturePattern[] = [];

  // Detect MVC pattern
  const mvcPattern = detectMVCPattern(files);
  if (mvcPattern) patterns.push(mvcPattern);

  // Detect Repository pattern
  const repositoryPattern = detectRepositoryPattern(files);
  if (repositoryPattern) patterns.push(repositoryPattern);

  // Detect Factory pattern
  const factoryPattern = detectFactoryPattern(files);
  if (factoryPattern) patterns.push(factoryPattern);

  // Detect Observer pattern
  const observerPattern = detectObserverPattern(files);
  if (observerPattern) patterns.push(observerPattern);

  // Detect Singleton pattern
  const singletonPattern = detectSingletonPattern(files);
  if (singletonPattern) patterns.push(singletonPattern);

  return patterns;
}

/**
 * Detect MVC (Model-View-Controller) pattern
 */
function detectMVCPattern(files: FileAnalysis[]): ArchitecturePattern | null {
  const models = files.filter(f =>
    f.path.includes('model') ||
    f.path.includes('entity') ||
    f.classes.some(c => c.name.toLowerCase().includes('model'))
  );

  const views = files.filter(f =>
    f.path.includes('view') ||
    f.path.includes('component') ||
    f.path.includes('template')
  );

  const controllers = files.filter(f =>
    f.path.includes('controller') ||
    f.path.includes('handler') ||
    f.classes.some(c => c.name.toLowerCase().includes('controller'))
  );

  if (models.length > 0 && views.length > 0 && controllers.length > 0) {
    const confidence = Math.min(1.0, (models.length + views.length + controllers.length) / files.length * 3);

    return {
      name: 'Model-View-Controller (MVC)',
      confidence: Math.round(confidence * 100) / 100,
      description: 'Separates application logic into three interconnected components',
      examples: [
        ...models.slice(0, 2).map(f => `Model: ${f.path}`),
        ...views.slice(0, 2).map(f => `View: ${f.path}`),
        ...controllers.slice(0, 2).map(f => `Controller: ${f.path}`)
      ]
    };
  }

  return null;
}

/**
 * Detect Repository pattern
 */
function detectRepositoryPattern(files: FileAnalysis[]): ArchitecturePattern | null {
  const repositories = files.filter(f =>
    f.path.toLowerCase().includes('repository') ||
    f.classes.some(c => c.name.toLowerCase().includes('repository'))
  );

  if (repositories.length > 0) {
    const confidence = Math.min(1.0, repositories.length / files.length * 10);

    return {
      name: 'Repository Pattern',
      confidence: Math.round(confidence * 100) / 100,
      description: 'Encapsulates data access logic and provides a uniform interface',
      examples: repositories.slice(0, 3).map(f => f.path)
    };
  }

  return null;
}

/**
 * Detect Factory pattern
 */
function detectFactoryPattern(files: FileAnalysis[]): ArchitecturePattern | null {
  const factories = files.filter(f =>
    f.path.toLowerCase().includes('factory') ||
    f.classes.some(c => c.name.toLowerCase().includes('factory')) ||
    f.functions.some(fn => fn.name.toLowerCase().includes('create'))
  );

  if (factories.length > 0) {
    const confidence = Math.min(1.0, factories.length / files.length * 8);

    return {
      name: 'Factory Pattern',
      confidence: Math.round(confidence * 100) / 100,
      description: 'Creates objects without specifying their concrete classes',
      examples: factories.slice(0, 3).map(f => f.path)
    };
  }

  return null;
}

/**
 * Detect Observer pattern
 */
function detectObserverPattern(files: FileAnalysis[]): ArchitecturePattern | null {
  const observers = files.filter(f =>
    f.functions.some(fn =>
      fn.name.toLowerCase().includes('subscribe') ||
      fn.name.toLowerCase().includes('notify') ||
      fn.name.toLowerCase().includes('emit')
    ) ||
    f.classes.some(c =>
      c.methods.some(m =>
        m.name.toLowerCase().includes('subscribe') ||
        m.name.toLowerCase().includes('notify')
      )
    )
  );

  if (observers.length > 0) {
    const confidence = Math.min(1.0, observers.length / files.length * 5);

    return {
      name: 'Observer Pattern',
      confidence: Math.round(confidence * 100) / 100,
      description: 'Defines a subscription mechanism to notify multiple objects',
      examples: observers.slice(0, 3).map(f => f.path)
    };
  }

  return null;
}

/**
 * Detect Singleton pattern
 */
function detectSingletonPattern(files: FileAnalysis[]): ArchitecturePattern | null {
  const singletons = files.filter(f =>
    f.classes.some(c =>
      c.methods.some(m => m.name.toLowerCase() === 'getinstance') ||
      c.name.toLowerCase().includes('singleton')
    )
  );

  if (singletons.length > 0) {
    const confidence = Math.min(1.0, singletons.length / files.length * 15);

    return {
      name: 'Singleton Pattern',
      confidence: Math.round(confidence * 100) / 100,
      description: 'Ensures a class has only one instance and provides global access',
      examples: singletons.slice(0, 3).map(f => f.path)
    };
  }

  return null;
}

/**
 * Check design principles violations
 */
function checkDesignPrinciples(
  files: FileAnalysis[],
  dependencies: DependencyGraph
): PrincipleViolation[] {
  const violations: PrincipleViolation[] = [];

  // Single Responsibility Principle
  violations.push(...checkSingleResponsibilityPrinciple(files));

  // Open/Closed Principle
  violations.push(...checkOpenClosedPrinciple(files));

  // Dependency Inversion Principle
  violations.push(...checkDependencyInversionPrinciple(files, dependencies));

  // Don't Repeat Yourself (DRY)
  violations.push(...checkDRYPrinciple(files));

  return violations;
}

/**
 * Check Single Responsibility Principle
 */
function checkSingleResponsibilityPrinciple(files: FileAnalysis[]): PrincipleViolation[] {
  const violations: PrincipleViolation[] = [];

  for (const file of files) {
    // Check if file has too many responsibilities (classes + functions)
    const totalResponsibilities = file.classes.length + file.functions.length;

    if (totalResponsibilities > 10) {
      violations.push({
        principle: 'Single Responsibility Principle',
        severity: 'medium',
        description: `File has too many responsibilities (${totalResponsibilities} classes/functions)`,
        files: [file.path],
        suggestion: 'Consider splitting this file into smaller, more focused modules'
      });
    }

    // Check if classes have too many methods
    for (const cls of file.classes) {
      if (cls.methods.length > 15) {
        violations.push({
          principle: 'Single Responsibility Principle',
          severity: 'high',
          description: `Class ${cls.name} has too many methods (${cls.methods.length})`,
          files: [file.path],
          suggestion: `Consider extracting some methods from ${cls.name} into separate classes`
        });
      }
    }
  }

  return violations;
}

/**
 * Check Open/Closed Principle
 */
function checkOpenClosedPrinciple(files: FileAnalysis[]): PrincipleViolation[] {
  const violations: PrincipleViolation[] = [];

  // This is a simplified check - in reality, this would require more sophisticated analysis
  for (const file of files) {
    for (const cls of file.classes) {
      // Check for large switch statements or if-else chains
      const hasLargeSwitchStatements = cls.methods.some(method =>
        method.complexity > 10 && method.name.toLowerCase().includes('handle')
      );

      if (hasLargeSwitchStatements) {
        violations.push({
          principle: 'Open/Closed Principle',
          severity: 'medium',
          description: `Class ${cls.name} may have large conditional logic that could be replaced with polymorphism`,
          files: [file.path],
          suggestion: 'Consider using strategy pattern or polymorphism instead of large conditional statements'
        });
      }
    }
  }

  return violations;
}

/**
 * Check Dependency Inversion Principle
 */
function checkDependencyInversionPrinciple(
  files: FileAnalysis[],
  dependencies: DependencyGraph
): PrincipleViolation[] {
  const violations: PrincipleViolation[] = [];

  // Check for high-level modules depending on low-level modules
  const highLevelModules = files.filter(f =>
    f.path.includes('service') ||
    f.path.includes('controller') ||
    f.path.includes('business')
  );

  const lowLevelModules = files.filter(f =>
    f.path.includes('data') ||
    f.path.includes('repository') ||
    f.path.includes('dao')
  );

  for (const highLevel of highLevelModules) {
    const directDependencies = dependencies.edges.filter(edge =>
      edge.source === highLevel.path.replace(/\\/g, '/')
    );

    const dependsOnLowLevel = directDependencies.some(edge =>
      lowLevelModules.some(lowLevel =>
        edge.target === lowLevel.path.replace(/\\/g, '/')
      )
    );

    if (dependsOnLowLevel) {
      violations.push({
        principle: 'Dependency Inversion Principle',
        severity: 'medium',
        description: 'High-level module directly depends on low-level modules',
        files: [highLevel.path],
        suggestion: 'Introduce abstractions/interfaces to decouple high-level and low-level modules'
      });
    }
  }

  return violations;
}

/**
 * Check DRY (Don't Repeat Yourself) Principle
 */
function checkDRYPrinciple(files: FileAnalysis[]): PrincipleViolation[] {
  const violations: PrincipleViolation[] = [];

  // Simple duplicate detection based on function names
  const functionNames = new Map<string, string[]>();

  for (const file of files) {
    for (const func of file.functions) {
      const existing = functionNames.get(func.name) || [];
      existing.push(file.path);
      functionNames.set(func.name, existing);
    }
  }

  for (const [funcName, filePaths] of functionNames) {
    if (filePaths.length > 2 && funcName !== 'constructor' && funcName !== 'render') {
      violations.push({
        principle: 'DRY (Don\'t Repeat Yourself)',
        severity: 'low',
        description: `Function "${funcName}" appears in multiple files`,
        files: filePaths,
        suggestion: 'Consider extracting common functionality into a shared utility'
      });
    }
  }

  return violations;
}

/**
 * Generate architecture suggestions
 */
function generateArchitectureSuggestions(
  files: FileAnalysis[],
  dependencies: DependencyGraph,
  violations: PrincipleViolation[]
): ArchitectureSuggestion[] {
  const suggestions: ArchitectureSuggestion[] = [];

  // Suggest refactoring based on violations
  for (const violation of violations) {
    if (violation.severity === 'high') {
      suggestions.push({
        type: 'refactor',
        priority: 'high',
        description: violation.suggestion,
        files: violation.files,
        effort: 'large'
      });
    }
  }

  // Suggest extracting large files
  const largeFiles = files.filter(f => f.lines > 500);
  for (const file of largeFiles) {
    suggestions.push({
      type: 'extract',
      priority: 'medium',
      description: `Extract functionality from large file (${file.lines} lines)`,
      files: [file.path],
      effort: 'medium'
    });
  }

  return suggestions;
}

/**
 * Analyze layering architecture
 */
function analyzeLayering(files: FileAnalysis[], dependencies: DependencyGraph): LayerAnalysis {
  const layers = identifyLayers(files);
  const violations = detectLayerViolations(layers, dependencies);
  const cohesion = calculateCohesion(layers, dependencies);
  const coupling = calculateCoupling(layers, dependencies);

  return {
    layers,
    violations,
    cohesion: Math.round(cohesion * 100) / 100,
    coupling: Math.round(coupling * 100) / 100
  };
}

/**
 * Identify architectural layers
 */
function identifyLayers(files: FileAnalysis[]): Layer[] {
  const layers: Layer[] = [];

  // Presentation layer
  const presentationFiles = files.filter(f =>
    f.path.includes('view') ||
    f.path.includes('component') ||
    f.path.includes('controller')
  );

  if (presentationFiles.length > 0) {
    layers.push({
      name: 'Presentation',
      files: presentationFiles.map(f => f.path),
      dependencies: [],
      level: 1
    });
  }

  // Business layer
  const businessFiles = files.filter(f =>
    f.path.includes('service') ||
    f.path.includes('business') ||
    f.path.includes('logic')
  );

  if (businessFiles.length > 0) {
    layers.push({
      name: 'Business',
      files: businessFiles.map(f => f.path),
      dependencies: [],
      level: 2
    });
  }

  // Data layer
  const dataFiles = files.filter(f =>
    f.path.includes('data') ||
    f.path.includes('repository') ||
    f.path.includes('dao')
  );

  if (dataFiles.length > 0) {
    layers.push({
      name: 'Data',
      files: dataFiles.map(f => f.path),
      dependencies: [],
      level: 3
    });
  }

  return layers;
}

/**
 * Detect layer violations
 */
function detectLayerViolations(layers: Layer[], dependencies: DependencyGraph): LayerViolation[] {
  const violations: LayerViolation[] = [];

  // This would require more sophisticated analysis
  // For now, return empty array

  return violations;
}

/**
 * Calculate layer cohesion
 */
function calculateCohesion(layers: Layer[], dependencies: DependencyGraph): number {
  // Simplified cohesion calculation
  return 0.7;
}

/**
 * Calculate layer coupling
 */
function calculateCoupling(layers: Layer[], dependencies: DependencyGraph): number {
  // Simplified coupling calculation
  return 0.3;
}
