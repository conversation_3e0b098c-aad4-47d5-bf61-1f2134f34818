/**
 * Type definitions for code analysis system
 */

export interface CodebaseAnalysis {
  summary: AnalysisSummary;
  files: FileAnalysis[];
  dependencies: DependencyGraph;
  architecture: ArchitectureInsights;
  quality: QualityMetrics;
}

export interface AnalysisSummary {
  totalFiles: number;
  totalLines: number;
  totalFunctions: number;
  totalClasses: number;
  languages: LanguageDistribution[];
  analysisDate: string;
}

export interface LanguageDistribution {
  language: string;
  fileCount: number;
  lineCount: number;
  percentage: number;
}

export interface FileAnalysis {
  path: string;
  language: string;
  size: number;
  lines: number;
  functions: FunctionMetadata[];
  classes: ClassMetadata[];
  imports: ImportMetadata[];
  exports: ExportMetadata[];
  complexity: ComplexityMetrics;
  quality: FileQualityMetrics;
}

export interface FunctionMetadata {
  name: string;
  startLine: number;
  endLine: number;
  parameters: ParameterMetadata[];
  returnType?: string;
  complexity: number;
  isAsync: boolean;
  isExported: boolean;
  documentation?: string;
  calls: string[];
}

export interface ParameterMetadata {
  name: string;
  type?: string;
  optional: boolean;
  defaultValue?: string;
}

export interface ClassMetadata {
  name: string;
  startLine: number;
  endLine: number;
  methods: FunctionMetadata[];
  properties: PropertyMetadata[];
  extends?: string;
  implements: string[];
  isExported: boolean;
  documentation?: string;
}

export interface PropertyMetadata {
  name: string;
  type?: string;
  visibility: 'public' | 'private' | 'protected';
  isStatic: boolean;
  isReadonly: boolean;
}

export interface ImportMetadata {
  source: string;
  imports: string[];
  isDefault: boolean;
  isNamespace: boolean;
  line: number;
}

export interface ExportMetadata {
  name: string;
  type: 'function' | 'class' | 'variable' | 'type';
  isDefault: boolean;
  line: number;
}

export interface ComplexityMetrics {
  cyclomatic: number;
  cognitive: number;
  halstead: HalsteadMetrics;
  maintainabilityIndex: number;
}

export interface HalsteadMetrics {
  vocabulary: number;
  length: number;
  difficulty: number;
  effort: number;
  time: number;
  bugs: number;
}

export interface FileQualityMetrics {
  duplicateLines: number;
  duplicatePercentage: number;
  commentLines: number;
  commentPercentage: number;
  testCoverage?: number;
  codeSmells: CodeSmell[];
}

export interface CodeSmell {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  line: number;
  description: string;
  suggestion?: string;
}

export interface DependencyGraph {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
  cycles: DependencyCycle[];
  metrics: DependencyMetrics;
}

export interface DependencyNode {
  id: string;
  name: string;
  type: 'file' | 'module' | 'package';
  path: string;
  size: number;
  complexity: number;
}

export interface DependencyEdge {
  source: string;
  target: string;
  type: 'import' | 'call' | 'inheritance' | 'composition';
  strength: number;
  line?: number;
}

export interface DependencyCycle {
  nodes: string[];
  severity: 'low' | 'medium' | 'high';
  impact: number;
}

export interface DependencyMetrics {
  totalDependencies: number;
  circularDependencies: number;
  maxDepth: number;
  averageDependencies: number;
  instability: number;
  abstractness: number;
}

export interface ArchitectureInsights {
  patterns: ArchitecturePattern[];
  principles: PrincipleViolation[];
  suggestions: ArchitectureSuggestion[];
  layering: LayerAnalysis;
}

export interface ArchitecturePattern {
  name: string;
  confidence: number;
  description: string;
  examples: string[];
}

export interface PrincipleViolation {
  principle: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  files: string[];
  suggestion: string;
}

export interface ArchitectureSuggestion {
  type: 'refactor' | 'extract' | 'merge' | 'move';
  priority: 'low' | 'medium' | 'high';
  description: string;
  files: string[];
  effort: 'small' | 'medium' | 'large';
}

export interface LayerAnalysis {
  layers: Layer[];
  violations: LayerViolation[];
  cohesion: number;
  coupling: number;
}

export interface Layer {
  name: string;
  files: string[];
  dependencies: string[];
  level: number;
}

export interface LayerViolation {
  type: 'skip-layer' | 'circular' | 'wrong-direction';
  source: string;
  target: string;
  severity: 'low' | 'medium' | 'high';
}

export interface QualityMetrics {
  overall: OverallQuality;
  maintainability: MaintainabilityMetrics;
  reliability: ReliabilityMetrics;
  security: SecurityMetrics;
  performance: PerformanceMetrics;
}

export interface OverallQuality {
  score: number;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  trend: 'improving' | 'stable' | 'declining';
  issues: QualityIssue[];
}

export interface QualityIssue {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  count: number;
  description: string;
}

export interface MaintainabilityMetrics {
  index: number;
  complexity: number;
  duplication: number;
  testability: number;
  documentation: number;
}

export interface ReliabilityMetrics {
  bugProneness: number;
  errorHandling: number;
  testCoverage: number;
  codeStability: number;
}

export interface SecurityMetrics {
  vulnerabilities: SecurityVulnerability[];
  riskScore: number;
  compliance: ComplianceCheck[];
}

export interface SecurityVulnerability {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  file: string;
  line: number;
  description: string;
  cwe?: string;
}

export interface ComplianceCheck {
  standard: string;
  status: 'pass' | 'fail' | 'warning';
  details: string;
}

export interface PerformanceMetrics {
  algorithmicComplexity: AlgorithmicComplexity[];
  memoryUsage: MemoryUsagePattern[];
  ioOperations: IOOperation[];
  optimizationOpportunities: OptimizationOpportunity[];
}

export interface AlgorithmicComplexity {
  function: string;
  timeComplexity: string;
  spaceComplexity: string;
  confidence: number;
}

export interface MemoryUsagePattern {
  pattern: string;
  severity: 'low' | 'medium' | 'high';
  files: string[];
  description: string;
}

export interface IOOperation {
  type: 'file' | 'network' | 'database';
  location: string;
  line: number;
  async: boolean;
  cached: boolean;
}

export interface OptimizationOpportunity {
  type: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'small' | 'medium' | 'large';
  description: string;
  files: string[];
}
