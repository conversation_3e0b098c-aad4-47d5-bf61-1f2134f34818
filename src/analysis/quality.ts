/**
 * Quality metrics analysis - Calculate comprehensive quality metrics
 */

import { 
  FileAnalysis, 
  DependencyGraph, 
  ArchitectureInsights,
  QualityMetrics,
  OverallQuality,
  MaintainabilityMetrics,
  ReliabilityMetrics,
  SecurityMetrics,
  PerformanceMetrics,
  QualityIssue,
  SecurityVulnerability,
  ComplianceCheck,
  AlgorithmicComplexity,
  MemoryUsagePattern,
  IOOperation,
  OptimizationOpportunity
} from "./types.js";

/**
 * Calculate comprehensive quality metrics
 */
export async function calculateQualityMetrics(
  files: FileAnalysis[], 
  dependencies: DependencyGraph, 
  architecture: ArchitectureInsights
): Promise<QualityMetrics> {
  console.log(`📊 Calculating quality metrics for ${files.length} files...`);
  
  const maintainability = calculateMaintainabilityMetrics(files, dependencies);
  const reliability = calculateReliabilityMetrics(files, dependencies);
  const security = calculateSecurityMetrics(files);
  const performance = calculatePerformanceMetrics(files);
  const overall = calculateOverallQuality(maintainability, reliability, security, performance, architecture);
  
  console.log(`✅ Quality analysis complete - Overall score: ${overall.score}/100`);
  
  return {
    overall,
    maintainability,
    reliability,
    security,
    performance
  };
}

/**
 * Calculate overall quality score and grade
 */
function calculateOverallQuality(
  maintainability: MaintainabilityMetrics,
  reliability: ReliabilityMetrics,
  security: SecurityMetrics,
  performance: PerformanceMetrics,
  architecture: ArchitectureInsights
): OverallQuality {
  // Weighted average of different quality aspects
  const weights = {
    maintainability: 0.3,
    reliability: 0.25,
    security: 0.25,
    performance: 0.2
  };
  
  const score = Math.round(
    maintainability.index * weights.maintainability +
    reliability.codeStability * weights.reliability +
    (100 - security.riskScore) * weights.security +
    (100 - performance.algorithmicComplexity.length * 5) * weights.performance
  );
  
  const grade = getQualityGrade(score);
  const trend = 'stable'; // Would need historical data to determine trend
  const issues = collectQualityIssues(maintainability, reliability, security, performance, architecture);
  
  return {
    score: Math.max(0, Math.min(100, score)),
    grade,
    trend,
    issues
  };
}

/**
 * Get quality grade based on score
 */
function getQualityGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
}

/**
 * Collect quality issues from all metrics
 */
function collectQualityIssues(
  maintainability: MaintainabilityMetrics,
  reliability: ReliabilityMetrics,
  security: SecurityMetrics,
  performance: PerformanceMetrics,
  architecture: ArchitectureInsights
): QualityIssue[] {
  const issues: QualityIssue[] = [];
  
  // Maintainability issues
  if (maintainability.complexity > 15) {
    issues.push({
      type: 'High Complexity',
      severity: 'high',
      count: 1,
      description: 'Average complexity is too high, making code hard to maintain'
    });
  }
  
  if (maintainability.duplication > 10) {
    issues.push({
      type: 'Code Duplication',
      severity: 'medium',
      count: 1,
      description: 'Significant code duplication detected'
    });
  }
  
  // Reliability issues
  if (reliability.testCoverage < 70) {
    issues.push({
      type: 'Low Test Coverage',
      severity: 'high',
      count: 1,
      description: 'Test coverage is below recommended threshold'
    });
  }
  
  // Security issues
  if (security.vulnerabilities.length > 0) {
    const criticalVulns = security.vulnerabilities.filter(v => v.severity === 'critical').length;
    const highVulns = security.vulnerabilities.filter(v => v.severity === 'high').length;
    
    if (criticalVulns > 0) {
      issues.push({
        type: 'Critical Security Vulnerabilities',
        severity: 'critical',
        count: criticalVulns,
        description: 'Critical security vulnerabilities found'
      });
    }
    
    if (highVulns > 0) {
      issues.push({
        type: 'High Security Vulnerabilities',
        severity: 'high',
        count: highVulns,
        description: 'High severity security vulnerabilities found'
      });
    }
  }
  
  // Performance issues
  const highComplexityAlgorithms = performance.algorithmicComplexity.filter(
    ac => ac.timeComplexity.includes('O(n²)') || ac.timeComplexity.includes('O(n³)')
  ).length;
  
  if (highComplexityAlgorithms > 0) {
    issues.push({
      type: 'Poor Algorithmic Complexity',
      severity: 'medium',
      count: highComplexityAlgorithms,
      description: 'Algorithms with poor time complexity detected'
    });
  }
  
  // Architecture issues
  const highSeverityViolations = architecture.principles.filter(p => p.severity === 'high').length;
  if (highSeverityViolations > 0) {
    issues.push({
      type: 'Architecture Principle Violations',
      severity: 'high',
      count: highSeverityViolations,
      description: 'High severity violations of design principles'
    });
  }
  
  return issues;
}

/**
 * Calculate maintainability metrics
 */
function calculateMaintainabilityMetrics(
  files: FileAnalysis[], 
  dependencies: DependencyGraph
): MaintainabilityMetrics {
  let totalComplexity = 0;
  let totalDuplication = 0;
  let totalDocumentation = 0;
  let totalLines = 0;
  
  for (const file of files) {
    totalComplexity += file.complexity.cyclomatic;
    totalDuplication += file.quality.duplicatePercentage;
    totalDocumentation += file.quality.commentPercentage;
    totalLines += file.lines;
  }
  
  const averageComplexity = files.length > 0 ? totalComplexity / files.length : 0;
  const averageDuplication = files.length > 0 ? totalDuplication / files.length : 0;
  const averageDocumentation = files.length > 0 ? totalDocumentation / files.length : 0;
  
  // Calculate maintainability index (simplified)
  const maintainabilityIndex = Math.max(0, 
    100 - averageComplexity * 2 - averageDuplication - (100 - averageDocumentation)
  );
  
  // Calculate testability based on complexity and dependencies
  const testability = Math.max(0, 100 - averageComplexity - dependencies.metrics.averageDependencies * 5);
  
  return {
    index: Math.round(maintainabilityIndex * 100) / 100,
    complexity: Math.round(averageComplexity * 100) / 100,
    duplication: Math.round(averageDuplication * 100) / 100,
    testability: Math.round(testability * 100) / 100,
    documentation: Math.round(averageDocumentation * 100) / 100
  };
}

/**
 * Calculate reliability metrics
 */
function calculateReliabilityMetrics(
  files: FileAnalysis[], 
  dependencies: DependencyGraph
): ReliabilityMetrics {
  // Calculate bug proneness based on complexity and code smells
  let totalCodeSmells = 0;
  let totalComplexity = 0;
  
  for (const file of files) {
    totalCodeSmells += file.quality.codeSmells.length;
    totalComplexity += file.complexity.cyclomatic;
  }
  
  const bugProneness = Math.min(100, (totalCodeSmells * 10) + (totalComplexity / files.length));
  
  // Simplified error handling assessment
  const errorHandling = 75; // Would need more sophisticated analysis
  
  // Simplified test coverage (would need actual test data)
  const testCoverage = 60; // Placeholder
  
  // Code stability based on complexity and dependencies
  const codeStability = Math.max(0, 100 - bugProneness - dependencies.metrics.circularDependencies * 10);
  
  return {
    bugProneness: Math.round(bugProneness * 100) / 100,
    errorHandling: Math.round(errorHandling * 100) / 100,
    testCoverage: Math.round(testCoverage * 100) / 100,
    codeStability: Math.round(codeStability * 100) / 100
  };
}

/**
 * Calculate security metrics
 */
function calculateSecurityMetrics(files: FileAnalysis[]): SecurityMetrics {
  const vulnerabilities: SecurityVulnerability[] = [];
  
  // Simple security vulnerability detection
  for (const file of files) {
    // Check for potential SQL injection
    if (file.path.includes('.sql') || 
        file.functions.some(f => f.calls.some(c => c.includes('query') || c.includes('execute')))) {
      vulnerabilities.push({
        type: 'Potential SQL Injection',
        severity: 'high',
        file: file.path,
        line: 1,
        description: 'Database queries detected - ensure proper parameterization',
        cwe: 'CWE-89'
      });
    }
    
    // Check for potential XSS
    if (file.functions.some(f => f.calls.some(c => c.includes('innerHTML') || c.includes('eval')))) {
      vulnerabilities.push({
        type: 'Potential XSS',
        severity: 'medium',
        file: file.path,
        line: 1,
        description: 'Dynamic content insertion detected - ensure proper sanitization',
        cwe: 'CWE-79'
      });
    }
  }
  
  const riskScore = Math.min(100, vulnerabilities.length * 15);
  
  const compliance: ComplianceCheck[] = [
    {
      standard: 'OWASP Top 10',
      status: vulnerabilities.length === 0 ? 'pass' : 'warning',
      details: `${vulnerabilities.length} potential vulnerabilities found`
    }
  ];
  
  return {
    vulnerabilities,
    riskScore,
    compliance
  };
}

/**
 * Calculate performance metrics
 */
function calculatePerformanceMetrics(files: FileAnalysis[]): PerformanceMetrics {
  const algorithmicComplexity: AlgorithmicComplexity[] = [];
  const memoryUsage: MemoryUsagePattern[] = [];
  const ioOperations: IOOperation[] = [];
  const optimizationOpportunities: OptimizationOpportunity[] = [];
  
  for (const file of files) {
    // Analyze algorithmic complexity
    for (const func of file.functions) {
      if (func.complexity > 10) {
        algorithmicComplexity.push({
          function: `${file.path}:${func.name}`,
          timeComplexity: func.complexity > 20 ? 'O(n²)' : 'O(n)',
          spaceComplexity: 'O(1)',
          confidence: 0.7
        });
      }
    }
    
    // Check for memory usage patterns
    if (file.functions.some(f => f.calls.some(c => c.includes('map') || c.includes('filter')))) {
      memoryUsage.push({
        pattern: 'Array operations',
        severity: 'low',
        files: [file.path],
        description: 'Multiple array operations may create intermediate arrays'
      });
    }
    
    // Check for I/O operations
    if (file.functions.some(f => f.calls.some(c => c.includes('fetch') || c.includes('read')))) {
      ioOperations.push({
        type: 'network',
        location: file.path,
        line: 1,
        async: file.functions.some(f => f.isAsync),
        cached: false
      });
    }
    
    // Identify optimization opportunities
    if (file.complexity.cyclomatic > 15) {
      optimizationOpportunities.push({
        type: 'Complexity Reduction',
        impact: 'high',
        effort: 'medium',
        description: 'High complexity functions could be optimized',
        files: [file.path]
      });
    }
  }
  
  return {
    algorithmicComplexity,
    memoryUsage,
    ioOperations,
    optimizationOpportunities
  };
}
