/**
 * Code parser - Extract metadata from source code
 */

import * as ts from "typescript";
import {
  FunctionMetadata,
  ClassMetadata,
  ImportMetadata,
  ExportMetadata,
  ComplexityMetrics,
  FileQualityMetrics,
  ParameterMetadata,
  PropertyMetadata,
  CodeSmell
} from "./types.js";

export interface ParseResult {
  functions: FunctionMetadata[];
  classes: ClassMetadata[];
  imports: ImportMetadata[];
  exports: ExportMetadata[];
  complexity: ComplexityMetrics;
  quality: FileQualityMetrics;
}

/**
 * Parse a source code file and extract metadata
 */
export async function parseFile(
  content: string,
  language: string,
  filePath: string
): Promise<ParseResult | null> {
  switch (language) {
    case 'typescript':
    case 'javascript':
      return parseTypeScript(content, filePath);
    case 'python':
      return parsePython(content, filePath);
    default:
      return parseGeneric(content, filePath);
  }
}

/**
 * Parse TypeScript/JavaScript files
 */
function parseTypeScript(content: string, filePath: string): ParseResult {
  const sourceFile = ts.createSourceFile(
    filePath,
    content,
    ts.ScriptTarget.Latest,
    true
  );

  const functions: FunctionMetadata[] = [];
  const classes: ClassMetadata[] = [];
  const imports: ImportMetadata[] = [];
  const exports: ExportMetadata[] = [];

  function visit(node: ts.Node) {
    switch (node.kind) {
      case ts.SyntaxKind.FunctionDeclaration:
        functions.push(parseFunctionDeclaration(node as ts.FunctionDeclaration, sourceFile));
        break;
      case ts.SyntaxKind.ClassDeclaration:
        classes.push(parseClassDeclaration(node as ts.ClassDeclaration, sourceFile));
        break;
      case ts.SyntaxKind.ImportDeclaration:
        imports.push(parseImportDeclaration(node as ts.ImportDeclaration, sourceFile));
        break;
      case ts.SyntaxKind.ExportDeclaration:
      case ts.SyntaxKind.ExportAssignment:
        exports.push(parseExportDeclaration(node, sourceFile));
        break;
    }

    ts.forEachChild(node, visit);
  }

  visit(sourceFile);

  const complexity = calculateComplexity(sourceFile, content);
  const quality = calculateQuality(content, sourceFile);

  return {
    functions,
    classes,
    imports,
    exports,
    complexity,
    quality
  };
}

/**
 * Parse function declaration
 */
function parseFunctionDeclaration(
  node: ts.FunctionDeclaration,
  sourceFile: ts.SourceFile
): FunctionMetadata {
  const startPos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
  const endPos = sourceFile.getLineAndCharacterOfPosition(node.getEnd());

  const parameters: ParameterMetadata[] = node.parameters.map(param => ({
    name: param.name.getText(),
    type: param.type?.getText(),
    optional: !!param.questionToken,
    defaultValue: param.initializer?.getText()
  }));

  const calls = extractFunctionCalls(node);

  return {
    name: node.name?.getText() || 'anonymous',
    startLine: startPos.line + 1,
    endLine: endPos.line + 1,
    parameters,
    returnType: node.type?.getText(),
    complexity: calculateCyclomaticComplexity(node),
    isAsync: !!node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.AsyncKeyword),
    isExported: !!node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword),
    documentation: extractDocumentation(node),
    calls
  };
}

/**
 * Parse class declaration
 */
function parseClassDeclaration(
  node: ts.ClassDeclaration,
  sourceFile: ts.SourceFile
): ClassMetadata {
  const startPos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
  const endPos = sourceFile.getLineAndCharacterOfPosition(node.getEnd());

  const methods: FunctionMetadata[] = [];
  const properties: PropertyMetadata[] = [];

  node.members.forEach(member => {
    if (ts.isMethodDeclaration(member)) {
      methods.push(parseFunctionDeclaration(member as any, sourceFile));
    } else if (ts.isPropertyDeclaration(member)) {
      properties.push(parsePropertyDeclaration(member, sourceFile));
    }
  });

  const extendsClause = node.heritageClauses?.find(
    clause => clause.token === ts.SyntaxKind.ExtendsKeyword
  );

  const implementsClauses = node.heritageClauses?.filter(
    clause => clause.token === ts.SyntaxKind.ImplementsKeyword
  ) || [];

  return {
    name: node.name?.getText() || 'anonymous',
    startLine: startPos.line + 1,
    endLine: endPos.line + 1,
    methods,
    properties,
    extends: extendsClause?.types[0]?.getText(),
    implements: implementsClauses.flatMap(clause =>
      clause.types.map(type => type.getText())
    ),
    isExported: !!node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword),
    documentation: extractDocumentation(node)
  };
}

/**
 * Parse property declaration
 */
function parsePropertyDeclaration(
  node: ts.PropertyDeclaration,
  sourceFile: ts.SourceFile
): PropertyMetadata {
  const visibility = getVisibility(node.modifiers);

  return {
    name: node.name.getText(),
    type: node.type?.getText(),
    visibility,
    isStatic: !!node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.StaticKeyword),
    isReadonly: !!node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ReadonlyKeyword)
  };
}

/**
 * Parse import declaration
 */
function parseImportDeclaration(
  node: ts.ImportDeclaration,
  sourceFile: ts.SourceFile
): ImportMetadata {
  const startPos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
  const source = node.moduleSpecifier.getText().replace(/['"]/g, '');

  const imports: string[] = [];
  let isDefault = false;
  let isNamespace = false;

  if (node.importClause) {
    if (node.importClause.name) {
      imports.push(node.importClause.name.getText());
      isDefault = true;
    }

    if (node.importClause.namedBindings) {
      if (ts.isNamespaceImport(node.importClause.namedBindings)) {
        imports.push(node.importClause.namedBindings.name.getText());
        isNamespace = true;
      } else if (ts.isNamedImports(node.importClause.namedBindings)) {
        node.importClause.namedBindings.elements.forEach(element => {
          imports.push(element.name.getText());
        });
      }
    }
  }

  return {
    source,
    imports,
    isDefault,
    isNamespace,
    line: startPos.line + 1
  };
}

/**
 * Parse export declaration
 */
function parseExportDeclaration(node: ts.Node, sourceFile: ts.SourceFile): ExportMetadata {
  const startPos = sourceFile.getLineAndCharacterOfPosition(node.getStart());

  // This is a simplified implementation
  // In a real implementation, you'd need to handle different export types
  return {
    name: 'export',
    type: 'variable',
    isDefault: false,
    line: startPos.line + 1
  };
}

/**
 * Extract function calls from a node
 */
function extractFunctionCalls(node: ts.Node): string[] {
  const calls: string[] = [];

  function visit(child: ts.Node) {
    if (ts.isCallExpression(child)) {
      const expression = child.expression.getText();
      calls.push(expression);
    }
    ts.forEachChild(child, visit);
  }

  ts.forEachChild(node, visit);
  return calls;
}

/**
 * Calculate cyclomatic complexity for a function
 */
function calculateCyclomaticComplexity(node: ts.Node): number {
  let complexity = 1; // Base complexity

  function visit(child: ts.Node) {
    switch (child.kind) {
      case ts.SyntaxKind.IfStatement:
      case ts.SyntaxKind.WhileStatement:
      case ts.SyntaxKind.ForStatement:
      case ts.SyntaxKind.ForInStatement:
      case ts.SyntaxKind.ForOfStatement:
      case ts.SyntaxKind.DoStatement:
      case ts.SyntaxKind.SwitchStatement:
      case ts.SyntaxKind.CatchClause:
      case ts.SyntaxKind.ConditionalExpression:
        complexity++;
        break;
      case ts.SyntaxKind.CaseClause:
        complexity++;
        break;
    }
    ts.forEachChild(child, visit);
  }

  ts.forEachChild(node, visit);
  return complexity;
}

/**
 * Calculate file complexity metrics
 */
function calculateComplexity(sourceFile: ts.SourceFile, content: string): ComplexityMetrics {
  let totalComplexity = 0;
  let functionCount = 0;

  function visit(node: ts.Node) {
    if (ts.isFunctionDeclaration(node) || ts.isMethodDeclaration(node)) {
      totalComplexity += calculateCyclomaticComplexity(node);
      functionCount++;
    }
    ts.forEachChild(node, visit);
  }

  visit(sourceFile);

  const averageComplexity = functionCount > 0 ? totalComplexity / functionCount : 0;

  return {
    cyclomatic: Math.round(averageComplexity * 100) / 100,
    cognitive: averageComplexity * 1.2, // Simplified cognitive complexity
    halstead: {
      vocabulary: 0,
      length: 0,
      difficulty: 0,
      effort: 0,
      time: 0,
      bugs: 0
    },
    maintainabilityIndex: Math.max(0, 171 - 5.2 * Math.log(averageComplexity) - 0.23 * totalComplexity)
  };
}

/**
 * Calculate file quality metrics
 */
function calculateQuality(content: string, sourceFile: ts.SourceFile): FileQualityMetrics {
  const lines = content.split('\n');
  const commentLines = lines.filter(line =>
    line.trim().startsWith('//') ||
    line.trim().startsWith('/*') ||
    line.trim().startsWith('*')
  ).length;

  const codeSmells: CodeSmell[] = [];

  // Simple code smell detection
  if (lines.length > 500) {
    codeSmells.push({
      type: 'large-file',
      severity: 'medium',
      line: 1,
      description: 'File is too large and should be split into smaller modules'
    });
  }

  return {
    duplicateLines: 0, // Would need more sophisticated analysis
    duplicatePercentage: 0,
    commentLines,
    commentPercentage: Math.round((commentLines / lines.length) * 100 * 100) / 100,
    codeSmells
  };
}

/**
 * Extract documentation from JSDoc comments
 */
function extractDocumentation(node: ts.Node): string | undefined {
  const sourceFile = node.getSourceFile();
  const fullText = sourceFile.getFullText();
  const commentRanges = ts.getLeadingCommentRanges(fullText, node.getFullStart());

  if (commentRanges && commentRanges.length > 0) {
    const lastComment = commentRanges[commentRanges.length - 1];
    return fullText.substring(lastComment.pos, lastComment.end);
  }

  return undefined;
}

/**
 * Get visibility modifier
 */
function getVisibility(modifiers?: ts.NodeArray<ts.ModifierLike>): 'public' | 'private' | 'protected' {
  if (!modifiers) return 'public';

  for (const modifier of modifiers) {
    if (ts.isModifier(modifier)) {
      switch (modifier.kind) {
        case ts.SyntaxKind.PrivateKeyword:
          return 'private';
        case ts.SyntaxKind.ProtectedKeyword:
          return 'protected';
      }
    }
  }

  return 'public';
}

/**
 * Simplified Python parser (placeholder)
 */
function parsePython(content: string, filePath: string): ParseResult {
  // This would require a Python AST parser
  // For now, return empty result
  return {
    functions: [],
    classes: [],
    imports: [],
    exports: [],
    complexity: {
      cyclomatic: 0,
      cognitive: 0,
      halstead: { vocabulary: 0, length: 0, difficulty: 0, effort: 0, time: 0, bugs: 0 },
      maintainabilityIndex: 100
    },
    quality: {
      duplicateLines: 0,
      duplicatePercentage: 0,
      commentLines: 0,
      commentPercentage: 0,
      codeSmells: []
    }
  };
}

/**
 * Generic parser for unsupported languages
 */
function parseGeneric(content: string, filePath: string): ParseResult {
  const lines = content.split('\n');

  return {
    functions: [],
    classes: [],
    imports: [],
    exports: [],
    complexity: {
      cyclomatic: 1,
      cognitive: 1,
      halstead: { vocabulary: 0, length: 0, difficulty: 0, effort: 0, time: 0, bugs: 0 },
      maintainabilityIndex: 100
    },
    quality: {
      duplicateLines: 0,
      duplicatePercentage: 0,
      commentLines: lines.filter(line => line.trim().startsWith('#') || line.trim().startsWith('//')).length,
      commentPercentage: 0,
      codeSmells: []
    }
  };
}
