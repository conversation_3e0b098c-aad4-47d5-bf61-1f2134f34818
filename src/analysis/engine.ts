/**
 * Code analysis engine - Core analysis functionality
 */

import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { CodebaseAnalysis, FileAnalysis, AnalysisSummary, LanguageDistribution } from "./types.js";
import { parseFile } from "./parser.js";
import { analyzeDependencies } from "./dependencies.js";
import { analyzeArchitecture } from "./architecture.js";
import { calculateQualityMetrics } from "./quality.js";

export class CodeAnalysisEngine {
  private rootPath: string;
  private excludePatterns: string[];
  private includeExtensions: string[];

  constructor(rootPath: string, options: AnalysisOptions = {}) {
    this.rootPath = rootPath;
    this.excludePatterns = options.excludePatterns || [
      'node_modules',
      '.git',
      'dist',
      'build',
      'coverage',
      '.next',
      '.nuxt'
    ];
    this.includeExtensions = options.includeExtensions || [
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.vue',
      '.py',
      '.java',
      '.cs',
      '.cpp',
      '.c',
      '.h'
    ];
  }

  /**
   * Perform comprehensive codebase analysis
   */
  async analyzeCodebase(): Promise<CodebaseAnalysis> {
    console.log(`🔍 Starting codebase analysis for: ${this.rootPath}`);
    
    // Step 1: Discover and analyze files
    const files = await this.discoverFiles();
    console.log(`📁 Found ${files.length} files to analyze`);
    
    const fileAnalyses: FileAnalysis[] = [];
    for (const file of files) {
      try {
        const analysis = await this.analyzeFile(file);
        if (analysis) {
          fileAnalyses.push(analysis);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to analyze ${file}: ${error}`);
      }
    }

    console.log(`✅ Successfully analyzed ${fileAnalyses.length} files`);

    // Step 2: Generate summary
    const summary = this.generateSummary(fileAnalyses);

    // Step 3: Analyze dependencies
    console.log(`🔗 Analyzing dependencies...`);
    const dependencies = await analyzeDependencies(fileAnalyses);

    // Step 4: Analyze architecture
    console.log(`🏗️ Analyzing architecture...`);
    const architecture = await analyzeArchitecture(fileAnalyses, dependencies);

    // Step 5: Calculate quality metrics
    console.log(`📊 Calculating quality metrics...`);
    const quality = await calculateQualityMetrics(fileAnalyses, dependencies, architecture);

    console.log(`🎉 Codebase analysis complete!`);

    return {
      summary,
      files: fileAnalyses,
      dependencies,
      architecture,
      quality
    };
  }

  /**
   * Analyze a single file
   */
  async analyzeFile(filePath: string): Promise<FileAnalysis | null> {
    const fullPath = path.resolve(this.rootPath, filePath);
    
    if (!existsSync(fullPath)) {
      return null;
    }

    const stats = await fs.stat(fullPath);
    const content = await fs.readFile(fullPath, 'utf-8');
    const language = this.detectLanguage(filePath);

    // Parse file content
    const parseResult = await parseFile(content, language, filePath);
    
    if (!parseResult) {
      return null;
    }

    return {
      path: filePath,
      language,
      size: stats.size,
      lines: content.split('\n').length,
      functions: parseResult.functions,
      classes: parseResult.classes,
      imports: parseResult.imports,
      exports: parseResult.exports,
      complexity: parseResult.complexity,
      quality: parseResult.quality
    };
  }

  /**
   * Discover all files to analyze
   */
  private async discoverFiles(): Promise<string[]> {
    const files: string[] = [];
    
    const walkDir = async (dir: string): Promise<void> => {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.relative(this.rootPath, fullPath);
        
        // Skip excluded patterns
        if (this.shouldExclude(relativePath)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await walkDir(fullPath);
        } else if (entry.isFile() && this.shouldInclude(entry.name)) {
          files.push(relativePath);
        }
      }
    };
    
    await walkDir(this.rootPath);
    return files;
  }

  /**
   * Check if path should be excluded
   */
  private shouldExclude(relativePath: string): boolean {
    return this.excludePatterns.some(pattern => 
      relativePath.includes(pattern) || 
      relativePath.startsWith(pattern)
    );
  }

  /**
   * Check if file should be included
   */
  private shouldInclude(fileName: string): boolean {
    const ext = path.extname(fileName);
    return this.includeExtensions.includes(ext);
  }

  /**
   * Detect programming language from file extension
   */
  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath);
    const languageMap: Record<string, string> = {
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.vue': 'vue',
      '.py': 'python',
      '.java': 'java',
      '.cs': 'csharp',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c'
    };
    
    return languageMap[ext] || 'unknown';
  }

  /**
   * Generate analysis summary
   */
  private generateSummary(fileAnalyses: FileAnalysis[]): AnalysisSummary {
    const languageStats = new Map<string, { files: number; lines: number }>();
    
    let totalLines = 0;
    let totalFunctions = 0;
    let totalClasses = 0;
    
    for (const file of fileAnalyses) {
      totalLines += file.lines;
      totalFunctions += file.functions.length;
      totalClasses += file.classes.length;
      
      const current = languageStats.get(file.language) || { files: 0, lines: 0 };
      current.files++;
      current.lines += file.lines;
      languageStats.set(file.language, current);
    }
    
    const languages: LanguageDistribution[] = Array.from(languageStats.entries()).map(
      ([language, stats]) => ({
        language,
        fileCount: stats.files,
        lineCount: stats.lines,
        percentage: Math.round((stats.lines / totalLines) * 100 * 100) / 100
      })
    ).sort((a, b) => b.lineCount - a.lineCount);
    
    return {
      totalFiles: fileAnalyses.length,
      totalLines,
      totalFunctions,
      totalClasses,
      languages,
      analysisDate: new Date().toISOString()
    };
  }
}

export interface AnalysisOptions {
  excludePatterns?: string[];
  includeExtensions?: string[];
  maxFileSize?: number;
  includeTests?: boolean;
  includeDocumentation?: boolean;
}

/**
 * Convenience function to analyze a codebase
 */
export async function analyzeCodebase(
  rootPath: string, 
  options?: AnalysisOptions
): Promise<CodebaseAnalysis> {
  const engine = new CodeAnalysisEngine(rootPath, options);
  return await engine.analyzeCodebase();
}
