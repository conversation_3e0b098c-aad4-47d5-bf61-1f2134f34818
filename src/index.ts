#!/usr/bin/env node

/**
 * Vibe Coding - MCP Server Entry Point
 * 
 * A comprehensive workflow automation system for AI-assisted development
 * Features:
 * - Steering system for persistent project knowledge
 * - Spec-driven development workflow (Requirements → Design → Tasks → Implementation)
 * - Bug fix workflow (Report → Analyze → Fix → Verify)
 */

import { startServer } from "./server.js";

async function main() {
  try {
    await startServer();
  } catch (error) {
    console.error("Fatal error in main():", error);
    process.exit(1);
  }
}

main();
