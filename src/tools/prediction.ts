/**
 * Prediction analysis MCP tools - Predictive problem identification
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { analyzeCodebase } from "../analysis/engine.js";
import { performPredictionAnalysis } from "../prediction/engine.js";
import { PredictionAnalysis } from "../prediction/types.js";

/**
 * Register prediction analysis tools
 */
export function registerPredictionTools(server: McpServer): void {
  // Predict issues tool
  server.tool(
    "predict-issues",
    "Predict potential problems before they occur using AI-driven analysis",
    {
      rootPath: z.string().describe("Project root directory path"),
      timeframe: z.enum(["immediate", "short-term", "medium-term", "long-term", "all"]).optional().describe("Prediction timeframe"),
      severity: z.enum(["low", "medium", "high", "critical", "all"]).optional().describe("Minimum severity level"),
      outputFormat: z.enum(["summary", "detailed", "json"]).optional().describe("Output format"),
    },
    async ({ rootPath, timeframe = "all", severity = "all", outputFormat = "detailed" }) => {
      try {
        console.log(`🔮 Predicting issues for timeframe: ${timeframe}, severity: ${severity}...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const predictions = await performPredictionAnalysis(analysis);
        
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(predictions.issues, null, 2),
              },
            ],
          };
        }
        
        const report = formatIssuesPredictionReport(predictions, timeframe, severity, outputFormat === "detailed");
        
        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to predict issues: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Assess performance tool
  server.tool(
    "assess-performance",
    "Assess performance risks and predict potential bottlenecks",
    {
      rootPath: z.string().describe("Project root directory path"),
      focus: z.enum(["bottlenecks", "scalability", "optimization", "all"]).optional().describe("Analysis focus"),
      outputFormat: z.enum(["summary", "detailed", "json"]).optional().describe("Output format"),
    },
    async ({ rootPath, focus = "all", outputFormat = "detailed" }) => {
      try {
        console.log(`⚡ Assessing performance risks with focus: ${focus}...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const predictions = await performPredictionAnalysis(analysis);
        
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(predictions.performance, null, 2),
              },
            ],
          };
        }
        
        const report = formatPerformanceAssessmentReport(predictions.performance, focus, outputFormat === "detailed");
        
        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to assess performance: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Scan security tool
  server.tool(
    "scan-security",
    "Scan for security vulnerabilities and potential threats",
    {
      rootPath: z.string().describe("Project root directory path"),
      scanType: z.enum(["vulnerabilities", "threats", "compliance", "all"]).optional().describe("Type of security scan"),
      severity: z.enum(["low", "medium", "high", "critical", "all"]).optional().describe("Minimum severity level"),
      outputFormat: z.enum(["summary", "detailed", "json"]).optional().describe("Output format"),
    },
    async ({ rootPath, scanType = "all", severity = "all", outputFormat = "detailed" }) => {
      try {
        console.log(`🔒 Scanning security with type: ${scanType}, severity: ${severity}...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const predictions = await performPredictionAnalysis(analysis);
        
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(predictions.security, null, 2),
              },
            ],
          };
        }
        
        const report = formatSecurityScanReport(predictions.security, scanType, severity, outputFormat === "detailed");
        
        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to scan security: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Measure debt tool
  server.tool(
    "measure-debt",
    "Measure and quantify technical debt with payoff strategies",
    {
      rootPath: z.string().describe("Project root directory path"),
      category: z.enum(["code", "architecture", "test", "documentation", "infrastructure", "all"]).optional().describe("Debt category to focus on"),
      outputFormat: z.enum(["summary", "detailed", "json"]).optional().describe("Output format"),
    },
    async ({ rootPath, category = "all", outputFormat = "detailed" }) => {
      try {
        console.log(`💳 Measuring technical debt for category: ${category}...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const predictions = await performPredictionAnalysis(analysis);
        
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(predictions.technicalDebt, null, 2),
              },
            ],
          };
        }
        
        const report = formatTechnicalDebtReport(predictions.technicalDebt, category, outputFormat === "detailed");
        
        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to measure debt: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * Format issues prediction report
 */
function formatIssuesPredictionReport(
  predictions: PredictionAnalysis, 
  timeframe: string, 
  severity: string, 
  detailed: boolean
): string {
  let filteredIssues = predictions.issues;
  
  // Filter by timeframe
  if (timeframe !== "all") {
    filteredIssues = filteredIssues.filter(issue => issue.timeframe === timeframe);
  }
  
  // Filter by severity
  if (severity !== "all") {
    filteredIssues = filteredIssues.filter(issue => issue.severity === severity);
  }
  
  let report = `# 🔮 Issue Prediction Report

## 📋 Summary
- **Total Predicted Issues**: ${filteredIssues.length}
- **Critical Issues**: ${filteredIssues.filter(i => i.severity === 'critical').length}
- **High Priority Issues**: ${filteredIssues.filter(i => i.severity === 'high').length}
- **Overall Risk**: ${predictions.summary.overallRisk}
- **Time to Action**: ${predictions.summary.timeToAction}
- **Prediction Confidence**: ${Math.round(predictions.summary.confidence * 100)}%

## 🎯 Issue Breakdown by Type
${getIssueTypeBreakdown(filteredIssues)}

## ⏰ Timeline Analysis
${getTimelineAnalysis(filteredIssues)}
`;

  if (detailed) {
    report += `
## 🔍 Detailed Issue Predictions
${filteredIssues.slice(0, 10).map(issue => `
### ${issue.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
- **Severity**: ${issue.severity} (${Math.round(issue.confidence * 100)}% confidence)
- **Timeframe**: ${issue.timeframe}
- **Location**: ${issue.location.file}${issue.location.function ? `:${issue.location.function}` : ''}
- **Description**: ${issue.description}
- **Impact**: ${issue.impact.scope} scope, ${issue.impact.userImpact} user impact
- **Prevention**: ${issue.prevention.priority} priority, ${issue.prevention.effort} effort
- **Timeline**: ${issue.prevention.timeline}

**Prevention Actions**:
${issue.prevention.actions.map(action => `- **${action.type}**: ${action.description} (${action.estimatedHours}h)`).join('\n')}
`).join('\n')}

## 💡 Top Recommendations
${predictions.summary.recommendations.slice(0, 5).map(rec => `- ${rec}`).join('\n')}
`;
  }

  report += `
## 📅 Next Steps
- **Next Review**: ${predictions.summary.nextReview}
- **Critical Actions**: ${predictions.summary.criticalIssues}
- **High Priority Actions**: ${predictions.summary.highPriorityActions}

---
*Prediction analysis completed at ${new Date().toLocaleString()}*
*Generated by Vibe Coding Prediction System*
`;

  return report;
}

/**
 * Format performance assessment report
 */
function formatPerformanceAssessmentReport(performance: any, focus: string, detailed: boolean): string {
  let report = `# ⚡ Performance Risk Assessment

## 📊 Overall Assessment
- **Risk Level**: ${performance.overallRisk}
- **Bottlenecks Found**: ${performance.bottlenecks.length}
- **Scalability Issues**: ${performance.scalabilityIssues.length}
- **Optimization Opportunities**: ${performance.optimizationOpportunities.length}

## 🚨 Critical Bottlenecks
${performance.bottlenecks.filter((b: any) => b.severity === 'critical' || b.severity === 'high').map((bottleneck: any) => 
  `- **${bottleneck.type}**: ${bottleneck.description} (${bottleneck.severity})`
).join('\n') || '- No critical bottlenecks detected'}

## 📈 Resource Usage Analysis
- **CPU**: ${performance.resourceUsage.cpu.current}% (trend: ${performance.resourceUsage.cpu.trend})
- **Memory**: ${performance.resourceUsage.memory.current}% (trend: ${performance.resourceUsage.memory.trend})
- **Database**: ${performance.resourceUsage.database.current}% (trend: ${performance.resourceUsage.database.trend})
`;

  if (detailed && (focus === "all" || focus === "bottlenecks")) {
    report += `
## 🔍 Detailed Bottleneck Analysis
${performance.bottlenecks.map((bottleneck: any) => `
### ${bottleneck.type.toUpperCase()} Bottleneck
- **Location**: ${bottleneck.location.file}${bottleneck.location.function ? `:${bottleneck.location.function}` : ''}
- **Severity**: ${bottleneck.severity}
- **Current Value**: ${bottleneck.metrics.currentValue} ${bottleneck.metrics.unit}
- **Threshold**: Warning at ${bottleneck.threshold.warning}, Critical at ${bottleneck.threshold.critical}
- **Prediction**: ${bottleneck.prediction.timeToThreshold} (${Math.round(bottleneck.prediction.confidence * 100)}% confidence)

**Mitigations**:
${bottleneck.prediction.mitigations.map((m: string) => `- ${m}`).join('\n')}
`).join('\n')}
`;
  }

  if (detailed && (focus === "all" || focus === "optimization")) {
    report += `
## 🎯 Optimization Opportunities
${performance.optimizationOpportunities.map((opt: any) => `
### ${opt.type.toUpperCase()} Optimization
- **Impact**: ${opt.impact}
- **Effort**: ${opt.effort}
- **Description**: ${opt.description}
- **Files**: ${opt.files.slice(0, 3).join(', ')}${opt.files.length > 3 ? ` (+${opt.files.length - 3} more)` : ''}

**Implementation Steps**:
${opt.implementation.steps.slice(0, 3).map((step: string) => `- ${step}`).join('\n')}
`).join('\n')}
`;
  }

  return report;
}

/**
 * Format security scan report
 */
function formatSecurityScanReport(security: any, scanType: string, severity: string, detailed: boolean): string {
  let filteredVulns = security.vulnerabilities;
  
  if (severity !== "all") {
    filteredVulns = filteredVulns.filter((v: any) => v.severity === severity);
  }
  
  let report = `# 🔒 Security Scan Report

## 🛡️ Security Overview
- **Overall Risk**: ${security.overallRisk}
- **Vulnerabilities Found**: ${filteredVulns.length}
- **Critical**: ${filteredVulns.filter((v: any) => v.severity === 'critical').length}
- **High**: ${filteredVulns.filter((v: any) => v.severity === 'high').length}
- **Medium**: ${filteredVulns.filter((v: any) => v.severity === 'medium').length}
- **Low**: ${filteredVulns.filter((v: any) => v.severity === 'low').length}

## 🚨 Critical Vulnerabilities
${filteredVulns.filter((v: any) => v.severity === 'critical' || v.severity === 'high').map((vuln: any) => 
  `- **${vuln.type}** (${vuln.severity}): ${vuln.description.substring(0, 100)}...`
).join('\n') || '- No critical vulnerabilities detected'}

## 🎯 Security Threats
${security.threats.map((threat: any) => 
  `- **${threat.type}** (${threat.impact} impact): ${threat.description}`
).join('\n')}
`;

  if (detailed) {
    report += `
## 🔍 Detailed Vulnerability Analysis
${filteredVulns.slice(0, 5).map((vuln: any) => `
### ${vuln.type}
- **Severity**: ${vuln.severity} (CVSS: ${vuln.cvss})
- **CWE**: ${vuln.cwe}
- **Location**: ${vuln.location.file}${vuln.location.function ? `:${vuln.location.function}` : ''}
- **Description**: ${vuln.description}
- **Exploitation**: ${vuln.exploitation.difficulty} difficulty, ${Math.round(vuln.exploitation.likelihood * 100)}% likelihood

**Remediation** (${vuln.remediation.priority} priority, ${vuln.remediation.effort} effort):
${vuln.remediation.steps.slice(0, 3).map((step: string) => `- ${step}`).join('\n')}
`).join('\n')}

## 📋 Compliance Assessment
${security.compliance.map((comp: any) => `
### ${comp.standard}
- **Status**: ${comp.status} (${comp.score}/100)
- **Gaps**: ${comp.gaps.length} identified
${comp.gaps.slice(0, 3).map((gap: any) => `  - ${gap.requirement}: ${gap.status}`).join('\n')}
`).join('\n')}
`;
  }

  return report;
}

/**
 * Format technical debt report
 */
function formatTechnicalDebtReport(debt: any, category: string, detailed: boolean): string {
  let filteredCategories = debt.categories;
  
  if (category !== "all") {
    filteredCategories = filteredCategories.filter((cat: any) => cat.type === category);
  }
  
  let report = `# 💳 Technical Debt Report

## 📊 Debt Overview
- **Total Debt**: ${debt.overallDebt.totalDebt} hours
- **Debt Ratio**: ${Math.round(debt.overallDebt.debtRatio * 100)}%
- **Interest Rate**: ${Math.round(debt.overallDebt.interestRate * 100)}% per month
- **Payoff Time**: ${debt.overallDebt.payoffTime}
- **Grade**: ${debt.overallDebt.grade}

## 📈 Debt by Category
${filteredCategories.map((cat: any) => 
  `- **${cat.type}**: ${cat.amount} hours (${cat.percentage}%) - ${cat.priority} priority`
).join('\n')}

## 🔥 Debt Hotspots
${debt.hotspots.slice(0, 5).map((hotspot: any) => 
  `- **${hotspot.location.file}**: ${hotspot.debtAmount}h debt, risk score ${hotspot.riskScore}`
).join('\n')}
`;

  if (detailed) {
    report += `
## 🔍 Detailed Debt Analysis
${filteredCategories.map((cat: any) => `
### ${cat.type.toUpperCase()} Debt (${cat.amount} hours)
${cat.items.slice(0, 5).map((item: any) => `
- **${item.description}**
  - Location: ${item.location.file}${item.location.function ? `:${item.location.function}` : ''}
  - Cost: ${item.cost} hours (${item.interest}h/month interest)
  - Effort: ${item.effort}, Impact: ${item.impact}
`).join('\n')}
`).join('\n')}

## 💰 Payoff Strategies
${debt.payoffStrategies.map((strategy: any) => `
### ${strategy.name}
- **Timeline**: ${strategy.timeline}
- **Effort**: ${strategy.effort} hours
- **ROI**: ${strategy.roi}x
- **Description**: ${strategy.description}

**Milestones**:
${strategy.milestones.map((milestone: any) => `- ${milestone.name} (${milestone.timeline})`).join('\n')}
`).join('\n')}
`;
  }

  return report;
}

/**
 * Helper functions
 */
function getIssueTypeBreakdown(issues: any[]): string {
  const typeCount = new Map<string, number>();
  issues.forEach(issue => {
    const type = issue.type.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
    typeCount.set(type, (typeCount.get(type) || 0) + 1);
  });
  
  return Array.from(typeCount.entries())
    .sort((a, b) => b[1] - a[1])
    .map(([type, count]) => `- **${type}**: ${count}`)
    .join('\n');
}

function getTimelineAnalysis(issues: any[]): string {
  const timeframes = ['immediate', 'short-term', 'medium-term', 'long-term'];
  return timeframes.map(timeframe => {
    const count = issues.filter(issue => issue.timeframe === timeframe).length;
    return `- **${timeframe}**: ${count} issues`;
  }).join('\n');
}
