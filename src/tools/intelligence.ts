/**
 * Intelligence analysis MCP tools - Advanced code analysis capabilities
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { analyzeCodebase } from "../analysis/engine.js";
import { CodebaseAnalysis } from "../analysis/types.js";

/**
 * Register intelligence analysis tools
 */
export function registerIntelligenceTools(server: McpServer): void {
  // Analyze codebase tool
  server.tool(
    "analyze-codebase",
    "Perform comprehensive codebase analysis including complexity, dependencies, architecture, and quality metrics",
    {
      rootPath: z.string().describe("Project root directory path"),
      includeTests: z.boolean().optional().describe("Include test files in analysis"),
      maxFileSize: z.number().optional().describe("Maximum file size to analyze (in KB)"),
      outputFormat: z.enum(["summary", "detailed", "json"]).optional().describe("Output format"),
    },
    async ({ rootPath, includeTests = false, maxFileSize = 1000, outputFormat = "detailed" }) => {
      try {
        console.log(`🔍 Starting comprehensive codebase analysis...`);
        
        const options = {
          includeTests,
          maxFileSize: maxFileSize * 1024, // Convert KB to bytes
          excludePatterns: includeTests ? 
            ['node_modules', '.git', 'dist', 'build', 'coverage'] :
            ['node_modules', '.git', 'dist', 'build', 'coverage', 'test', 'tests', '__tests__', 'spec']
        };
        
        const analysis = await analyzeCodebase(rootPath, options);
        
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(analysis, null, 2),
              },
            ],
          };
        }
        
        const report = formatAnalysisReport(analysis, outputFormat === "detailed");
        
        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to analyze codebase: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Map dependencies tool
  server.tool(
    "map-dependencies",
    "Generate detailed dependency mapping and visualization",
    {
      rootPath: z.string().describe("Project root directory path"),
      format: z.enum(["mermaid", "text", "json"]).optional().describe("Output format"),
      includeExternal: z.boolean().optional().describe("Include external dependencies"),
    },
    async ({ rootPath, format = "mermaid", includeExternal = false }) => {
      try {
        console.log(`🔗 Mapping dependencies...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const dependencyMap = formatDependencyMap(analysis.dependencies, format, includeExternal);
        
        return {
          content: [
            {
              type: "text",
              text: dependencyMap,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to map dependencies: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Assess architecture tool
  server.tool(
    "assess-architecture",
    "Evaluate architectural patterns, principles, and provide improvement suggestions",
    {
      rootPath: z.string().describe("Project root directory path"),
      focus: z.enum(["patterns", "principles", "suggestions", "all"]).optional().describe("Analysis focus"),
    },
    async ({ rootPath, focus = "all" }) => {
      try {
        console.log(`🏗️ Assessing architecture...`);
        
        const analysis = await analyzeCodebase(rootPath);
        const architectureReport = formatArchitectureReport(analysis.architecture, focus);
        
        return {
          content: [
            {
              type: "text",
              text: architectureReport,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to assess architecture: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Track evolution tool
  server.tool(
    "track-evolution",
    "Track project evolution and identify trends",
    {
      rootPath: z.string().describe("Project root directory path"),
      timeframe: z.enum(["week", "month", "quarter", "year"]).optional().describe("Analysis timeframe"),
    },
    async ({ rootPath, timeframe = "month" }) => {
      try {
        console.log(`📈 Tracking project evolution...`);
        
        // This would require historical data analysis
        // For now, provide current snapshot with evolution insights
        const analysis = await analyzeCodebase(rootPath);
        const evolutionReport = formatEvolutionReport(analysis, timeframe);
        
        return {
          content: [
            {
              type: "text",
              text: evolutionReport,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to track evolution: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * Format comprehensive analysis report
 */
function formatAnalysisReport(analysis: CodebaseAnalysis, detailed: boolean): string {
  const { summary, dependencies, architecture, quality } = analysis;
  
  let report = `# 📊 Codebase Analysis Report

## 📋 Summary
- **Total Files**: ${summary.totalFiles}
- **Total Lines**: ${summary.totalLines.toLocaleString()}
- **Total Functions**: ${summary.totalFunctions}
- **Total Classes**: ${summary.totalClasses}
- **Analysis Date**: ${new Date(summary.analysisDate).toLocaleString()}

## 🌐 Language Distribution
${summary.languages.map(lang => 
  `- **${lang.language}**: ${lang.fileCount} files (${lang.lineCount.toLocaleString()} lines, ${lang.percentage}%)`
).join('\n')}

## 📊 Quality Overview
- **Overall Score**: ${quality.overall.score}/100 (Grade: ${quality.overall.grade})
- **Maintainability Index**: ${quality.maintainability.index}/100
- **Test Coverage**: ${quality.reliability.testCoverage}%
- **Security Risk Score**: ${quality.security.riskScore}/100

## 🔗 Dependencies
- **Total Dependencies**: ${dependencies.metrics.totalDependencies}
- **Circular Dependencies**: ${dependencies.metrics.circularDependencies}
- **Max Dependency Depth**: ${dependencies.metrics.maxDepth}
- **Average Dependencies per File**: ${dependencies.metrics.averageDependencies}

## 🏗️ Architecture
- **Patterns Detected**: ${architecture.patterns.length}
- **Principle Violations**: ${architecture.principles.length}
- **Improvement Suggestions**: ${architecture.suggestions.length}
`;

  if (detailed) {
    report += `
## 🎯 Quality Issues
${quality.overall.issues.map(issue => 
  `- **${issue.type}** (${issue.severity}): ${issue.description} (${issue.count} occurrences)`
).join('\n')}

## 🔍 Architecture Patterns
${architecture.patterns.map(pattern => 
  `- **${pattern.name}** (${Math.round(pattern.confidence * 100)}% confidence): ${pattern.description}`
).join('\n')}

## ⚠️ Principle Violations
${architecture.principles.map(violation => 
  `- **${violation.principle}** (${violation.severity}): ${violation.description}`
).join('\n')}

## 💡 Improvement Suggestions
${architecture.suggestions.map(suggestion => 
  `- **${suggestion.type}** (${suggestion.priority} priority): ${suggestion.description} (${suggestion.effort} effort)`
).join('\n')}

## 🔒 Security Analysis
${quality.security.vulnerabilities.length > 0 ? 
  quality.security.vulnerabilities.map(vuln => 
    `- **${vuln.type}** (${vuln.severity}): ${vuln.description} in ${vuln.file}`
  ).join('\n') : 
  '- No security vulnerabilities detected'
}

## ⚡ Performance Analysis
${quality.performance.optimizationOpportunities.length > 0 ? 
  quality.performance.optimizationOpportunities.map(opp => 
    `- **${opp.type}** (${opp.impact} impact): ${opp.description}`
  ).join('\n') : 
  '- No major performance issues detected'
}
`;
  }

  report += `
## 🎯 Recommendations

### High Priority
${getHighPriorityRecommendations(analysis).map(rec => `- ${rec}`).join('\n')}

### Medium Priority
${getMediumPriorityRecommendations(analysis).map(rec => `- ${rec}`).join('\n')}

---
*Analysis completed at ${new Date().toLocaleString()}*
*Generated by Vibe Coding Intelligence System*
`;

  return report;
}

/**
 * Format dependency map
 */
function formatDependencyMap(dependencies: any, format: string, includeExternal: boolean): string {
  if (format === "mermaid") {
    let mermaidGraph = `# 🔗 Dependency Map

\`\`\`mermaid
graph TD
`;
    
    // Add nodes
    for (const node of dependencies.nodes.slice(0, 20)) { // Limit for readability
      const nodeId = node.id.replace(/[^a-zA-Z0-9]/g, '_');
      mermaidGraph += `    ${nodeId}["${node.name}"]\n`;
    }
    
    // Add edges
    for (const edge of dependencies.edges.slice(0, 30)) { // Limit for readability
      const sourceId = edge.source.replace(/[^a-zA-Z0-9]/g, '_');
      const targetId = edge.target.replace(/[^a-zA-Z0-9]/g, '_');
      mermaidGraph += `    ${sourceId} --> ${targetId}\n`;
    }
    
    mermaidGraph += `\`\`\`

## 📊 Dependency Statistics
- **Total Dependencies**: ${dependencies.metrics.totalDependencies}
- **Circular Dependencies**: ${dependencies.metrics.circularDependencies}
- **Max Depth**: ${dependencies.metrics.maxDepth}
- **Instability**: ${dependencies.metrics.instability}
`;

    if (dependencies.cycles.length > 0) {
      mermaidGraph += `
## ⚠️ Circular Dependencies
${dependencies.cycles.map((cycle: any) => 
  `- **${cycle.severity}** severity: ${cycle.nodes.join(' → ')}`
).join('\n')}
`;
    }

    return mermaidGraph;
  }
  
  // Text format
  return `# 🔗 Dependency Analysis

## Dependencies Overview
${dependencies.nodes.map((node: any) => 
  `- ${node.name} (${node.type}): ${node.complexity} complexity`
).join('\n')}

## Dependency Relationships
${dependencies.edges.map((edge: any) => 
  `- ${edge.source} → ${edge.target} (${edge.type}, strength: ${edge.strength})`
).join('\n')}
`;
}

/**
 * Format architecture report
 */
function formatArchitectureReport(architecture: any, focus: string): string {
  let report = `# 🏗️ Architecture Assessment Report\n\n`;
  
  if (focus === "patterns" || focus === "all") {
    report += `## 🎯 Detected Patterns
${architecture.patterns.map((pattern: any) => 
  `- **${pattern.name}** (${Math.round(pattern.confidence * 100)}% confidence)\n  ${pattern.description}`
).join('\n\n')}

`;
  }
  
  if (focus === "principles" || focus === "all") {
    report += `## ⚖️ Design Principle Violations
${architecture.principles.map((violation: any) => 
  `- **${violation.principle}** (${violation.severity} severity)\n  ${violation.description}\n  💡 ${violation.suggestion}`
).join('\n\n')}

`;
  }
  
  if (focus === "suggestions" || focus === "all") {
    report += `## 💡 Improvement Suggestions
${architecture.suggestions.map((suggestion: any) => 
  `- **${suggestion.type}** (${suggestion.priority} priority, ${suggestion.effort} effort)\n  ${suggestion.description}`
).join('\n\n')}

`;
  }
  
  return report;
}

/**
 * Format evolution report
 */
function formatEvolutionReport(analysis: CodebaseAnalysis, timeframe: string): string {
  return `# 📈 Project Evolution Report

## Current Snapshot (${timeframe} analysis)
- **Codebase Size**: ${analysis.summary.totalLines.toLocaleString()} lines
- **Complexity**: ${analysis.quality.maintainability.complexity}/100
- **Quality Score**: ${analysis.quality.overall.score}/100
- **Architecture Health**: ${100 - analysis.architecture.principles.length * 10}/100

## Evolution Insights
- **Growth Trend**: Stable (would require historical data)
- **Quality Trend**: ${analysis.quality.overall.trend}
- **Complexity Trend**: Monitoring recommended
- **Technical Debt**: ${analysis.architecture.principles.length} issues identified

## Recommendations for Next ${timeframe}
- Focus on reducing complexity in high-complexity modules
- Address ${analysis.quality.overall.issues.length} quality issues
- Implement ${analysis.architecture.suggestions.length} architecture improvements
- Monitor dependency growth and circular dependencies

---
*Evolution tracking requires historical data for accurate trend analysis*
`;
}

/**
 * Get high priority recommendations
 */
function getHighPriorityRecommendations(analysis: CodebaseAnalysis): string[] {
  const recommendations: string[] = [];
  
  if (analysis.quality.overall.score < 70) {
    recommendations.push("Improve overall code quality - current score is below acceptable threshold");
  }
  
  if (analysis.dependencies.metrics.circularDependencies > 0) {
    recommendations.push(`Resolve ${analysis.dependencies.metrics.circularDependencies} circular dependencies`);
  }
  
  const criticalIssues = analysis.quality.overall.issues.filter(issue => issue.severity === 'critical');
  if (criticalIssues.length > 0) {
    recommendations.push(`Address ${criticalIssues.length} critical quality issues immediately`);
  }
  
  const highViolations = analysis.architecture.principles.filter(p => p.severity === 'high');
  if (highViolations.length > 0) {
    recommendations.push(`Fix ${highViolations.length} high-severity architecture violations`);
  }
  
  return recommendations;
}

/**
 * Get medium priority recommendations
 */
function getMediumPriorityRecommendations(analysis: CodebaseAnalysis): string[] {
  const recommendations: string[] = [];
  
  if (analysis.quality.maintainability.complexity > 10) {
    recommendations.push("Reduce code complexity through refactoring");
  }
  
  if (analysis.quality.reliability.testCoverage < 80) {
    recommendations.push("Increase test coverage to improve reliability");
  }
  
  if (analysis.quality.maintainability.documentation < 20) {
    recommendations.push("Improve code documentation and comments");
  }
  
  const mediumSuggestions = analysis.architecture.suggestions.filter(s => s.priority === 'medium');
  if (mediumSuggestions.length > 0) {
    recommendations.push(`Consider implementing ${mediumSuggestions.length} architecture improvements`);
  }
  
  return recommendations;
}
