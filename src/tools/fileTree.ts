/**
 * File tree generation utilities
 */

import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { parseGitignore } from "../utils.js";

// Default blacklist when .gitignore doesn't exist
const folderBlackList = [
  "node_modules",
  ".codelf",
  ".git",
  ".idea",
  ".vscode",
  "dist",
  "build",
  "out",
  "target",
  "bin",
  "obj",
  ".next",
  "coverage",
  "__pycache__",
  ".DS_Store",
  "tmp",
  "temp",
  "logs",
  ".cache",
  ".github",
  ".gitlab",
  "vendor",
];

const forceBlackList = [".git", ".codelf", ".vscode", ".idea"];

/**
 * Generate file tree for the project
 */
export async function getFileTree(rootPath: string): Promise<string> {
  const indent = "    ";

  // Recursively process a single path (directory or file)
  const processEntry = async (entryPath: string, displayName: string, prefix: string, relativePath: string): Promise<string[]> => {
    const stat = await fs.stat(entryPath).catch(() => null);
    const lines: string[] = [];
    if (stat && stat.isDirectory()) {
      lines.push(`${prefix}- ${displayName}`);
      const entries = await fs.readdir(entryPath, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory() && forceBlackList.includes(entry.name)) continue;
        const entryRelativePath = path.join(relativePath, entry.name).replace(/\\/g, "/");
        const subPath = path.join(entryPath, entry.name);
        lines.push(...(await processEntry(subPath, entry.name, prefix + indent, entryRelativePath)));
      }
    } else if (stat && stat.isFile()) {
      lines.push(`${prefix}- ${displayName}`);
    }
    return lines;
  };

  const buildTree = async (
    dir: string,
    prefix: string,
    relativePath: string = ""
  ): Promise<string[]> => {
    const codelfPath = path.join(rootPath, ".codelf.config");
    const result: string[] = [];
    const existsCodelfFile = existsSync(codelfPath) && !(await fs.stat(codelfPath)).isDirectory();

    if (existsCodelfFile && dir === rootPath) {
      // Read .codelf.config file content
      const content = await fs.readFile(codelfPath, "utf-8");
      const lines = content
        .split(/\r?\n/)
        .map((l) => l.trim())
        .filter((l) => l && !l.startsWith("#"));
      if (lines.length) {
        for (const line of lines) {
          const entryPath = path.join(rootPath, line);
          result.push(...(await processEntry(entryPath, line, prefix, line.replace(/\\/g, "/"))));
        }
        return result;
      }
    }

    // Original recursive logic
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
      if (entry.isDirectory() && forceBlackList.includes(entry.name)) {
        continue;
      }
      
      // Try to parse .gitignore file
      // If .gitignore exists and parsing succeeds, use its rules; otherwise use default blacklist
      const entryRelativePath = path
        .join(relativePath, entry.name)
        .replace(/\\/g, "/");
      const isIgnore = await parseGitignore(rootPath, entryRelativePath);

      // Use .gitignore rules or default blacklist for filtering
      const shouldIgnore =
        typeof isIgnore === "boolean"
          ? isIgnore
          : folderBlackList.includes(entry.name);
      if (!shouldIgnore) {
        const entryPath = path.join(dir, entry.name);
        result.push(...(await processEntry(entryPath, entry.name, prefix, entryRelativePath)));
      }
    }

    return result;
  };

  const result = await buildTree(rootPath, "", "");
  return ["root", ...result].join("\n");
}
