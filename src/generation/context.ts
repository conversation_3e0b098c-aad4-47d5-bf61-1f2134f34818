/**
 * 智能代码生成 - 上下文分析器
 * 提取项目上下文、风格指南和架构模式
 */

import * as fs from "fs";
import * as path from "path";
import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import {
  ProjectStyleGuide,
  NamingConventions,
  FormattingRules,
  ArchitecturalConventions,
  DocumentationConventions
} from "./types.js";

/**
 * 项目上下文信息
 */
export interface ProjectContext {
  /** 项目根路径 */
  rootPath: string;
  /** 项目类型 */
  projectType: ProjectType;
  /** 主要技术栈 */
  techStack: string[];
  /** 风格指南 */
  styleGuide: ProjectStyleGuide;
  /** 配置文件 */
  configFiles: ConfigFile[];
  /** 项目结构模式 */
  structurePatterns: string[];
}

/**
 * 项目类型
 */
export type ProjectType = 
  | "web-frontend"     // Web前端项目
  | "web-backend"      // Web后端项目
  | "mobile-app"       // 移动应用
  | "desktop-app"      // 桌面应用
  | "library"          // 库项目
  | "cli-tool"         // 命令行工具
  | "microservice"     // 微服务
  | "monorepo";        // 单体仓库

/**
 * 配置文件信息
 */
export interface ConfigFile {
  /** 文件路径 */
  path: string;
  /** 配置类型 */
  type: ConfigType;
  /** 配置内容 */
  content: any;
}

/**
 * 配置文件类型
 */
export type ConfigType = 
  | "package.json"
  | "tsconfig.json"
  | "eslint"
  | "prettier"
  | "babel"
  | "webpack"
  | "vite"
  | "jest"
  | "cypress";

/**
 * 提取项目上下文
 */
export async function extractProjectContext(
  analysis: CodebaseAnalysis,
  projectRoot: string
): Promise<ProjectContext> {
  console.log(`🔍 Extracting project context from ${projectRoot}...`);
  
  // 检测项目类型
  const projectType = detectProjectType(analysis, projectRoot);
  
  // 提取技术栈
  const techStack = extractTechStack(analysis, projectRoot);
  
  // 分析风格指南
  const styleGuide = await analyzeStyleGuide(analysis, projectRoot);
  
  // 读取配置文件
  const configFiles = await readConfigFiles(projectRoot);
  
  // 分析项目结构模式
  const structurePatterns = analyzeStructurePatterns(analysis);
  
  console.log(`✅ Project context extracted - Type: ${projectType}, Tech: ${techStack.join(', ')}`);
  
  return {
    rootPath: projectRoot,
    projectType,
    techStack,
    styleGuide,
    configFiles,
    structurePatterns
  };
}

/**
 * 检测项目类型
 */
function detectProjectType(analysis: CodebaseAnalysis, projectRoot: string): ProjectType {
  const packageJsonPath = path.join(projectRoot, "package.json");
  
  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf-8"));
      
      // 检查依赖来判断项目类型
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };
      
      // React/Vue 前端项目
      if (dependencies.react || dependencies.vue || dependencies["@angular/core"]) {
        return "web-frontend";
      }
      
      // Express/Koa 后端项目
      if (dependencies.express || dependencies.koa || dependencies.fastify) {
        return "web-backend";
      }
      
      // React Native 移动应用
      if (dependencies["react-native"] || dependencies.expo) {
        return "mobile-app";
      }
      
      // Electron 桌面应用
      if (dependencies.electron) {
        return "desktop-app";
      }
      
      // CLI 工具
      if (packageJson.bin || dependencies.commander || dependencies.yargs) {
        return "cli-tool";
      }
      
      // 库项目
      if (packageJson.main && !packageJson.scripts?.start) {
        return "library";
      }
      
      // Lerna/Nx 单体仓库
      if (dependencies.lerna || dependencies["@nrwl/workspace"]) {
        return "monorepo";
      }
    } catch (error) {
      console.warn(`Failed to parse package.json: ${error}`);
    }
  }
  
  // 基于文件结构判断
  const hasDockerfile = analysis.files.some(f => f.path.includes("Dockerfile"));
  if (hasDockerfile) {
    return "microservice";
  }
  
  // 默认为库项目
  return "library";
}

/**
 * 提取技术栈
 */
function extractTechStack(analysis: CodebaseAnalysis, projectRoot: string): string[] {
  const techStack: Set<string> = new Set();
  
  // 从文件扩展名推断
  const extensions = new Set(
    analysis.files.map(f => path.extname(f.path).toLowerCase())
  );
  
  if (extensions.has(".ts") || extensions.has(".tsx")) {
    techStack.add("TypeScript");
  }
  if (extensions.has(".js") || extensions.has(".jsx")) {
    techStack.add("JavaScript");
  }
  if (extensions.has(".py")) {
    techStack.add("Python");
  }
  if (extensions.has(".java")) {
    techStack.add("Java");
  }
  if (extensions.has(".go")) {
    techStack.add("Go");
  }
  if (extensions.has(".rs")) {
    techStack.add("Rust");
  }
  
  // 从 package.json 推断框架
  const packageJsonPath = path.join(projectRoot, "package.json");
  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf-8"));
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };
      
      // 前端框架
      if (dependencies.react) techStack.add("React");
      if (dependencies.vue) techStack.add("Vue");
      if (dependencies["@angular/core"]) techStack.add("Angular");
      if (dependencies.svelte) techStack.add("Svelte");
      
      // 后端框架
      if (dependencies.express) techStack.add("Express");
      if (dependencies.koa) techStack.add("Koa");
      if (dependencies.fastify) techStack.add("Fastify");
      if (dependencies.nestjs) techStack.add("NestJS");
      
      // 构建工具
      if (dependencies.webpack) techStack.add("Webpack");
      if (dependencies.vite) techStack.add("Vite");
      if (dependencies.rollup) techStack.add("Rollup");
      if (dependencies.parcel) techStack.add("Parcel");
      
      // 测试框架
      if (dependencies.jest) techStack.add("Jest");
      if (dependencies.mocha) techStack.add("Mocha");
      if (dependencies.vitest) techStack.add("Vitest");
      if (dependencies.cypress) techStack.add("Cypress");
      
      // 数据库
      if (dependencies.mongoose) techStack.add("MongoDB");
      if (dependencies.pg || dependencies.mysql2) techStack.add("SQL");
      if (dependencies.redis) techStack.add("Redis");
      
    } catch (error) {
      console.warn(`Failed to extract tech stack from package.json: ${error}`);
    }
  }
  
  return Array.from(techStack);
}

/**
 * 分析风格指南
 */
async function analyzeStyleGuide(
  analysis: CodebaseAnalysis,
  projectRoot: string
): Promise<ProjectStyleGuide> {
  console.log(`📋 Analyzing project style guide...`);
  
  // 分析命名约定
  const namingConventions = analyzeNamingConventions(analysis);
  
  // 分析格式化规则
  const formatting = await analyzeFormattingRules(projectRoot);
  
  // 分析架构约定
  const architecturalConventions = analyzeArchitecturalConventions(analysis);
  
  // 分析文档约定
  const documentationConventions = analyzeDocumentationConventions(analysis);
  
  return {
    namingConventions,
    formatting,
    architecturalConventions,
    documentationConventions
  };
}

/**
 * 分析命名约定
 */
function analyzeNamingConventions(analysis: CodebaseAnalysis): NamingConventions {
  const functionNames: string[] = [];
  const variableNames: string[] = [];
  const classNames: string[] = [];
  const fileNames: string[] = [];
  
  // 收集命名样本
  analysis.files.forEach(file => {
    // 文件名
    const fileName = path.basename(file.path, path.extname(file.path));
    fileNames.push(fileName);
    
    // 函数和类名（从分析结果中提取）
    if (file.functions) {
      file.functions.forEach(func => functionNames.push(func.name));
    }
    if (file.classes) {
      file.classes.forEach(cls => classNames.push(cls.name));
    }
  });
  
  return {
    functions: detectNamingPattern(functionNames) as any,
    variables: detectNamingPattern(variableNames) as any,
    classes: detectNamingPattern(classNames) as any,
    files: detectNamingPattern(fileNames) as any,
    constants: "UPPER_CASE" // 默认
  };
}

/**
 * 检测命名模式
 */
function detectNamingPattern(names: string[]): string {
  if (names.length === 0) return "camelCase";
  
  let camelCaseCount = 0;
  let snakeCaseCount = 0;
  let pascalCaseCount = 0;
  let kebabCaseCount = 0;
  
  names.forEach(name => {
    if (/^[a-z][a-zA-Z0-9]*$/.test(name)) camelCaseCount++;
    if (/^[a-z][a-z0-9_]*$/.test(name)) snakeCaseCount++;
    if (/^[A-Z][a-zA-Z0-9]*$/.test(name)) pascalCaseCount++;
    if (/^[a-z][a-z0-9-]*$/.test(name)) kebabCaseCount++;
  });
  
  const max = Math.max(camelCaseCount, snakeCaseCount, pascalCaseCount, kebabCaseCount);
  
  if (max === pascalCaseCount) return "PascalCase";
  if (max === snakeCaseCount) return "snake_case";
  if (max === kebabCaseCount) return "kebab-case";
  return "camelCase";
}

/**
 * 分析格式化规则
 */
async function analyzeFormattingRules(projectRoot: string): Promise<FormattingRules> {
  // 默认格式化规则
  let formatting: FormattingRules = {
    indentation: "spaces",
    indentSize: 2,
    lineLength: 100,
    semicolons: "always",
    quotes: "double"
  };
  
  // 读取 Prettier 配置
  const prettierConfigPaths = [
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.js",
    "prettier.config.js"
  ];
  
  for (const configPath of prettierConfigPaths) {
    const fullPath = path.join(projectRoot, configPath);
    if (fs.existsSync(fullPath)) {
      try {
        let config;
        if (configPath.endsWith(".js")) {
          // 动态导入 JS 配置文件
          config = require(fullPath);
        } else {
          config = JSON.parse(fs.readFileSync(fullPath, "utf-8"));
        }
        
        if (config.useTabs) formatting.indentation = "tabs";
        if (config.tabWidth) formatting.indentSize = config.tabWidth;
        if (config.printWidth) formatting.lineLength = config.printWidth;
        if (config.semi === false) formatting.semicolons = "never";
        if (config.singleQuote) formatting.quotes = "single";
        
        break;
      } catch (error) {
        console.warn(`Failed to parse Prettier config: ${error}`);
      }
    }
  }
  
  // 读取 ESLint 配置
  const eslintConfigPaths = [
    ".eslintrc",
    ".eslintrc.json",
    ".eslintrc.js",
    "eslint.config.js"
  ];
  
  for (const configPath of eslintConfigPaths) {
    const fullPath = path.join(projectRoot, configPath);
    if (fs.existsSync(fullPath)) {
      try {
        let config;
        if (configPath.endsWith(".js")) {
          config = require(fullPath);
        } else {
          config = JSON.parse(fs.readFileSync(fullPath, "utf-8"));
        }
        
        const rules = config.rules || {};
        
        if (rules.indent) {
          const indentRule = rules.indent;
          if (Array.isArray(indentRule) && indentRule[1] === "tab") {
            formatting.indentation = "tabs";
          } else if (Array.isArray(indentRule) && typeof indentRule[1] === "number") {
            formatting.indentSize = indentRule[1];
          }
        }
        
        if (rules.semi) {
          const semiRule = rules.semi;
          if (Array.isArray(semiRule) && semiRule[1] === "never") {
            formatting.semicolons = "never";
          }
        }
        
        if (rules.quotes) {
          const quotesRule = rules.quotes;
          if (Array.isArray(quotesRule)) {
            formatting.quotes = quotesRule[1] === "single" ? "single" : "double";
          }
        }
        
        break;
      } catch (error) {
        console.warn(`Failed to parse ESLint config: ${error}`);
      }
    }
  }
  
  return formatting;
}

/**
 * 分析架构约定
 */
function analyzeArchitecturalConventions(analysis: CodebaseAnalysis): ArchitecturalConventions {
  // 分析目录结构
  const directories = new Set<string>();
  analysis.files.forEach(file => {
    const dir = path.dirname(file.path);
    if (dir !== ".") {
      directories.add(dir.split("/")[0]); // 顶级目录
    }
  });
  
  // 分析导入模式
  const importPatterns: string[] = [];
  analysis.files.forEach(file => {
    if (file.imports) {
      file.imports.forEach(imp => {
        if (imp.startsWith("./") || imp.startsWith("../")) {
          importPatterns.push("relative");
        } else if (imp.startsWith("@/")) {
          importPatterns.push("alias");
        } else {
          importPatterns.push("absolute");
        }
      });
    }
  });
  
  return {
    directoryStructure: Array.from(directories),
    importPatterns: [...new Set(importPatterns)],
    errorHandlingPatterns: ["try-catch"], // 默认
    asyncPatterns: ["async-await"] // 默认
  };
}

/**
 * 分析文档约定
 */
function analyzeDocumentationConventions(analysis: CodebaseAnalysis): DocumentationConventions {
  let hasJSDoc = false;
  let hasInlineComments = false;
  let hasBlockComments = false;
  
  // 分析注释风格（需要从实际代码中分析）
  analysis.files.forEach(file => {
    // 这里需要实际的代码内容分析
    // 简化实现
    if (file.functions && file.functions.some(f => f.documentation)) {
      hasJSDoc = true;
    }
  });
  
  const commentStyle = hasJSDoc ? "jsdoc" : hasBlockComments ? "block" : "inline";
  
  return {
    commentStyle,
    documentationCoverage: "standard",
    readmeTemplate: undefined
  };
}

/**
 * 读取配置文件
 */
async function readConfigFiles(projectRoot: string): Promise<ConfigFile[]> {
  const configFiles: ConfigFile[] = [];
  
  const configPaths = [
    { path: "package.json", type: "package.json" as ConfigType },
    { path: "tsconfig.json", type: "tsconfig.json" as ConfigType },
    { path: ".eslintrc.json", type: "eslint" as ConfigType },
    { path: ".prettierrc", type: "prettier" as ConfigType },
    { path: "babel.config.js", type: "babel" as ConfigType },
    { path: "webpack.config.js", type: "webpack" as ConfigType },
    { path: "vite.config.js", type: "vite" as ConfigType },
    { path: "jest.config.js", type: "jest" as ConfigType },
    { path: "cypress.config.js", type: "cypress" as ConfigType }
  ];
  
  for (const config of configPaths) {
    const fullPath = path.join(projectRoot, config.path);
    if (fs.existsSync(fullPath)) {
      try {
        let content;
        if (config.path.endsWith(".js")) {
          // 对于 JS 配置文件，我们只记录存在性
          content = { exists: true };
        } else {
          content = JSON.parse(fs.readFileSync(fullPath, "utf-8"));
        }
        
        configFiles.push({
          path: config.path,
          type: config.type,
          content
        });
      } catch (error) {
        console.warn(`Failed to read config file ${config.path}: ${error}`);
      }
    }
  }
  
  return configFiles;
}

/**
 * 分析项目结构模式
 */
function analyzeStructurePatterns(analysis: CodebaseAnalysis): string[] {
  const patterns: Set<string> = new Set();
  
  // 检查常见的项目结构模式
  const directories = analysis.files.map(f => path.dirname(f.path));
  const uniqueDirectories = [...new Set(directories)];
  
  // MVC 模式
  if (uniqueDirectories.some(d => d.includes("models")) &&
      uniqueDirectories.some(d => d.includes("views")) &&
      uniqueDirectories.some(d => d.includes("controllers"))) {
    patterns.add("MVC");
  }
  
  // 分层架构
  if (uniqueDirectories.some(d => d.includes("services")) &&
      uniqueDirectories.some(d => d.includes("repositories"))) {
    patterns.add("Layered Architecture");
  }
  
  // 组件化架构
  if (uniqueDirectories.some(d => d.includes("components"))) {
    patterns.add("Component-based");
  }
  
  // 模块化架构
  if (uniqueDirectories.some(d => d.includes("modules"))) {
    patterns.add("Modular");
  }
  
  // 微服务架构
  if (uniqueDirectories.some(d => d.includes("services")) &&
      analysis.files.some(f => f.path.includes("docker"))) {
    patterns.add("Microservices");
  }
  
  return Array.from(patterns);
}
