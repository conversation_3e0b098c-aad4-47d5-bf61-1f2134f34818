/**
 * 智能代码生成 - 重构建议引擎
 * 基于预测分析生成智能重构建议
 */

import { CodebaseAnalysis, FileAnalysis, FunctionInfo } from "../analysis/types.js";
import { PredictionAnalysis } from "../prediction/types.js";
import { RefactorSuggestion, RefactorType, RefactorImpact, RiskLevel } from "./types.js";

/**
 * 生成重构建议
 */
export async function generateRefactorSuggestions(
  codebaseAnalysis: CodebaseAnalysis,
  predictionAnalysis: PredictionAnalysis,
  targetFile?: string
): Promise<RefactorSuggestion[]> {
  console.log(`🔄 Generating refactor suggestions${targetFile ? ` for ${targetFile}` : ''}...`);
  
  const suggestions: RefactorSuggestion[] = [];
  
  // 基于预测分析的问题生成重构建议
  const issueBasedSuggestions = generateIssueBasedRefactors(predictionAnalysis, codebaseAnalysis);
  suggestions.push(...issueBasedSuggestions);
  
  // 基于代码质量的重构建议
  const qualityBasedSuggestions = generateQualityBasedRefactors(codebaseAnalysis, targetFile);
  suggestions.push(...qualityBasedSuggestions);
  
  // 基于架构模式的重构建议
  const architectureBasedSuggestions = generateArchitectureBasedRefactors(codebaseAnalysis);
  suggestions.push(...architectureBasedSuggestions);
  
  // 排序建议（按优先级和风险）
  const sortedSuggestions = sortRefactorSuggestions(suggestions);
  
  console.log(`✅ Generated ${sortedSuggestions.length} refactor suggestions`);
  return sortedSuggestions;
}

/**
 * 基于预测分析问题生成重构建议
 */
function generateIssueBasedRefactors(
  predictionAnalysis: PredictionAnalysis,
  codebaseAnalysis: CodebaseAnalysis
): RefactorSuggestion[] {
  const suggestions: RefactorSuggestion[] = [];
  
  // 基于问题预测生成重构建议
  for (const issue of predictionAnalysis.issues) {
    if (issue.type === 'bug_prone_code') {
      suggestions.push(createExtractMethodSuggestion(issue, codebaseAnalysis));
    } else if (issue.type === 'maintainability_decline') {
      suggestions.push(createSplitClassSuggestion(issue, codebaseAnalysis));
    } else if (issue.type === 'code_duplication') {
      suggestions.push(createConsolidateDuplicateSuggestion(issue, codebaseAnalysis));
    }
  }
  
  // 基于性能预测生成重构建议
  for (const bottleneck of predictionAnalysis.performance.bottlenecks) {
    if (bottleneck.type === 'algorithm') {
      suggestions.push(createAlgorithmOptimizationSuggestion(bottleneck, codebaseAnalysis));
    } else if (bottleneck.type === 'memory') {
      suggestions.push(createMemoryOptimizationSuggestion(bottleneck, codebaseAnalysis));
    }
  }
  
  return suggestions;
}

/**
 * 基于代码质量生成重构建议
 */
function generateQualityBasedRefactors(
  codebaseAnalysis: CodebaseAnalysis,
  targetFile?: string
): RefactorSuggestion[] {
  const suggestions: RefactorSuggestion[] = [];
  
  const filesToAnalyze = targetFile 
    ? codebaseAnalysis.files.filter(f => f.path === targetFile)
    : codebaseAnalysis.files;
  
  for (const file of filesToAnalyze) {
    // 检查长函数
    if (file.functions) {
      for (const func of file.functions) {
        if (func.complexity > 10) {
          suggestions.push(createExtractMethodFromComplexFunction(func, file));
        }
      }
    }
    
    // 检查大类
    if (file.classes) {
      for (const cls of file.classes) {
        if (cls.methods && cls.methods.length > 15) {
          suggestions.push(createSplitLargeClass(cls, file));
        }
      }
    }
    
    // 检查命名问题
    const namingSuggestions = analyzeNamingIssues(file);
    suggestions.push(...namingSuggestions);
  }
  
  return suggestions;
}

/**
 * 基于架构模式生成重构建议
 */
function generateArchitectureBasedRefactors(codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion[] {
  const suggestions: RefactorSuggestion[] = [];
  
  // 检查循环依赖
  if (codebaseAnalysis.dependencies) {
    const circularDeps = findCircularDependencies(codebaseAnalysis.dependencies);
    for (const cycle of circularDeps) {
      suggestions.push(createBreakCircularDependencySuggestion(cycle, codebaseAnalysis));
    }
  }
  
  // 检查违反单一职责原则的类
  const srpViolations = findSRPViolations(codebaseAnalysis);
  for (const violation of srpViolations) {
    suggestions.push(createSRPRefactorSuggestion(violation, codebaseAnalysis));
  }
  
  return suggestions;
}

/**
 * 创建提取方法建议
 */
function createExtractMethodSuggestion(issue: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  const targetFile = findFileByPath(issue.location.file, codebaseAnalysis);
  
  return {
    type: "extract_method",
    targetFile: issue.location.file,
    description: `提取复杂方法以提高可读性和可维护性`,
    beforeCode: `// 复杂的方法实现
function complexMethod() {
  // 大量复杂逻辑...
  // 超过 20 行代码
}`,
    afterCode: `// 重构后的方法
function complexMethod() {
  const step1Result = performStep1();
  const step2Result = performStep2(step1Result);
  return finalizeResult(step2Result);
}

function performStep1() {
  // 步骤1的逻辑
}

function performStep2(input: any) {
  // 步骤2的逻辑
}

function finalizeResult(input: any) {
  // 最终处理逻辑
}`,
    impact: {
      affectedFiles: [issue.location.file],
      affectedFunctions: [issue.location.function],
      affectedClasses: [],
      breakingChanges: false,
      testImpact: "minor"
    },
    risk: "low",
    benefits: [
      "提高代码可读性",
      "增强可测试性",
      "便于代码复用",
      "降低维护成本"
    ]
  };
}

/**
 * 创建拆分类建议
 */
function createSplitClassSuggestion(issue: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "extract_class",
    targetFile: issue.location.file,
    description: `拆分大类以遵循单一职责原则`,
    beforeCode: `// 职责过多的大类
class UserManager {
  // 用户管理
  createUser() { }
  updateUser() { }
  deleteUser() { }
  
  // 邮件发送
  sendWelcomeEmail() { }
  sendPasswordResetEmail() { }
  
  // 数据验证
  validateEmail() { }
  validatePassword() { }
}`,
    afterCode: `// 拆分后的类
class UserManager {
  constructor(
    private emailService: EmailService,
    private validator: UserValidator
  ) {}
  
  createUser() {
    // 使用 validator 和 emailService
  }
  updateUser() { }
  deleteUser() { }
}

class EmailService {
  sendWelcomeEmail() { }
  sendPasswordResetEmail() { }
}

class UserValidator {
  validateEmail() { }
  validatePassword() { }
}`,
    impact: {
      affectedFiles: [issue.location.file],
      affectedFunctions: [],
      affectedClasses: [issue.location.class],
      breakingChanges: true,
      testImpact: "major"
    },
    risk: "medium",
    benefits: [
      "遵循单一职责原则",
      "提高代码内聚性",
      "便于单元测试",
      "增强代码复用性"
    ]
  };
}

/**
 * 创建合并重复代码建议
 */
function createConsolidateDuplicateSuggestion(issue: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "consolidate_duplicate",
    targetFile: issue.location.file,
    description: `合并重复代码以减少维护成本`,
    beforeCode: `// 重复的代码块
function processUserData(user: User) {
  if (!user.email) throw new Error('Email required');
  if (!user.name) throw new Error('Name required');
  // 处理逻辑...
}

function processAdminData(admin: Admin) {
  if (!admin.email) throw new Error('Email required');
  if (!admin.name) throw new Error('Name required');
  // 处理逻辑...
}`,
    afterCode: `// 提取公共验证逻辑
function validateRequiredFields(data: { email?: string; name?: string }) {
  if (!data.email) throw new Error('Email required');
  if (!data.name) throw new Error('Name required');
}

function processUserData(user: User) {
  validateRequiredFields(user);
  // 处理逻辑...
}

function processAdminData(admin: Admin) {
  validateRequiredFields(admin);
  // 处理逻辑...
}`,
    impact: {
      affectedFiles: [issue.location.file],
      affectedFunctions: issue.duplicateLocations || [],
      affectedClasses: [],
      breakingChanges: false,
      testImpact: "minor"
    },
    risk: "low",
    benefits: [
      "减少代码重复",
      "降低维护成本",
      "提高一致性",
      "便于统一修改"
    ]
  };
}

/**
 * 从复杂函数创建提取方法建议
 */
function createExtractMethodFromComplexFunction(func: FunctionInfo, file: FileAnalysis): RefactorSuggestion {
  return {
    type: "extract_method",
    targetFile: file.path,
    description: `提取复杂函数 ${func.name} 中的子方法`,
    beforeCode: `// 复杂度: ${func.complexity}
function ${func.name}() {
  // 复杂的实现逻辑
  // 建议拆分为多个小方法
}`,
    afterCode: `// 重构后的方法
function ${func.name}() {
  const result1 = step1();
  const result2 = step2(result1);
  return step3(result2);
}

function step1() {
  // 步骤1实现
}

function step2(input: any) {
  // 步骤2实现
}

function step3(input: any) {
  // 步骤3实现
}`,
    impact: {
      affectedFiles: [file.path],
      affectedFunctions: [func.name],
      affectedClasses: [],
      breakingChanges: false,
      testImpact: "minor"
    },
    risk: "low",
    benefits: [
      "降低圈复杂度",
      "提高可读性",
      "增强可测试性"
    ]
  };
}

/**
 * 创建拆分大类建议
 */
function createSplitLargeClass(cls: any, file: FileAnalysis): RefactorSuggestion {
  return {
    type: "extract_class",
    targetFile: file.path,
    description: `拆分大类 ${cls.name}（${cls.methods?.length || 0} 个方法）`,
    beforeCode: `class ${cls.name} {
  // ${cls.methods?.length || 0} 个方法
  // 建议按职责拆分
}`,
    afterCode: `// 拆分后的类
class ${cls.name} {
  // 核心职责方法
}

class ${cls.name}Helper {
  // 辅助功能方法
}`,
    impact: {
      affectedFiles: [file.path],
      affectedFunctions: [],
      affectedClasses: [cls.name],
      breakingChanges: true,
      testImpact: "major"
    },
    risk: "medium",
    benefits: [
      "遵循单一职责原则",
      "提高内聚性",
      "便于维护"
    ]
  };
}

/**
 * 分析命名问题
 */
function analyzeNamingIssues(file: FileAnalysis): RefactorSuggestion[] {
  const suggestions: RefactorSuggestion[] = [];
  
  // 检查函数命名
  if (file.functions) {
    for (const func of file.functions) {
      if (func.name.length < 3 || /^[a-z]$/.test(func.name)) {
        suggestions.push({
          type: "rename",
          targetFile: file.path,
          description: `重命名函数 ${func.name} 以提高可读性`,
          beforeCode: `function ${func.name}() { }`,
          afterCode: `function descriptiveFunctionName() { }`,
          impact: {
            affectedFiles: [file.path],
            affectedFunctions: [func.name],
            affectedClasses: [],
            breakingChanges: false,
            testImpact: "minor"
          },
          risk: "low",
          benefits: ["提高代码可读性", "增强代码自文档化"]
        });
      }
    }
  }
  
  return suggestions;
}

/**
 * 排序重构建议
 */
function sortRefactorSuggestions(suggestions: RefactorSuggestion[]): RefactorSuggestion[] {
  return suggestions.sort((a, b) => {
    // 按风险级别排序（低风险优先）
    const riskOrder = { low: 0, medium: 1, high: 2, critical: 3 };
    const riskDiff = riskOrder[a.risk] - riskOrder[b.risk];
    
    if (riskDiff !== 0) return riskDiff;
    
    // 按收益数量排序（收益多的优先）
    return b.benefits.length - a.benefits.length;
  });
}

// ============================================================================
// 辅助函数
// ============================================================================

function findFileByPath(path: string, codebaseAnalysis: CodebaseAnalysis): FileAnalysis | undefined {
  return codebaseAnalysis.files.find(f => f.path === path);
}

function findCircularDependencies(dependencies: any[]): any[] {
  // 简化实现，实际需要图算法检测循环
  return [];
}

function findSRPViolations(codebaseAnalysis: CodebaseAnalysis): any[] {
  // 简化实现，实际需要分析类的职责
  return [];
}

function createAlgorithmOptimizationSuggestion(bottleneck: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "replace_conditional",
    targetFile: bottleneck.location?.file || "unknown",
    description: "优化算法以提高性能",
    beforeCode: "// 低效算法实现",
    afterCode: "// 优化后的算法实现",
    impact: {
      affectedFiles: [bottleneck.location?.file || "unknown"],
      affectedFunctions: [],
      affectedClasses: [],
      breakingChanges: false,
      testImpact: "minor"
    },
    risk: "medium",
    benefits: ["提高执行性能", "减少资源消耗"]
  };
}

function createMemoryOptimizationSuggestion(bottleneck: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "replace_conditional",
    targetFile: bottleneck.location?.file || "unknown",
    description: "优化内存使用",
    beforeCode: "// 内存使用较多的实现",
    afterCode: "// 内存优化后的实现",
    impact: {
      affectedFiles: [bottleneck.location?.file || "unknown"],
      affectedFunctions: [],
      affectedClasses: [],
      breakingChanges: false,
      testImpact: "minor"
    },
    risk: "low",
    benefits: ["减少内存占用", "提高性能"]
  };
}

function createBreakCircularDependencySuggestion(cycle: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "move_method",
    targetFile: "multiple",
    description: "打破循环依赖",
    beforeCode: "// 存在循环依赖的代码",
    afterCode: "// 重构后消除循环依赖",
    impact: {
      affectedFiles: cycle.files || [],
      affectedFunctions: [],
      affectedClasses: [],
      breakingChanges: true,
      testImpact: "major"
    },
    risk: "high",
    benefits: ["消除循环依赖", "提高架构清晰度"]
  };
}

function createSRPRefactorSuggestion(violation: any, codebaseAnalysis: CodebaseAnalysis): RefactorSuggestion {
  return {
    type: "extract_class",
    targetFile: violation.file || "unknown",
    description: "重构以遵循单一职责原则",
    beforeCode: "// 违反SRP的类",
    afterCode: "// 重构后遵循SRP的类",
    impact: {
      affectedFiles: [violation.file || "unknown"],
      affectedFunctions: [],
      affectedClasses: [violation.class || "unknown"],
      breakingChanges: true,
      testImpact: "major"
    },
    risk: "medium",
    benefits: ["遵循SOLID原则", "提高代码质量"]
  };
}
