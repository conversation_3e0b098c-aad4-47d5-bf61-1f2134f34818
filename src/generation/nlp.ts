/**
 * 智能代码生成 - NLP 处理模块
 * 解析用户描述并提取代码结构信息
 */

import { GenerationType, ProgrammingLanguage } from "./types.js";

/**
 * 代码结构信息
 */
export interface CodeStructure {
  /** 主要名称 */
  name: string;
  /** 描述 */
  description: string;
  /** 参数信息 */
  parameters: ParameterInfo[];
  /** 返回类型 */
  returnType?: string;
  /** 属性信息 */
  properties: PropertyInfo[];
  /** 方法信息 */
  methods: MethodInfo[];
  /** 是否异步 */
  isAsync: boolean;
  /** 依赖项 */
  dependencies: string[];
  /** 复杂度级别 */
  complexity: "simple" | "medium" | "complex";
}

/**
 * 参数信息
 */
export interface ParameterInfo {
  name: string;
  type: string;
  description: string;
  isOptional: boolean;
  defaultValue?: string;
}

/**
 * 属性信息
 */
export interface PropertyInfo {
  name: string;
  type: string;
  description: string;
  isOptional: boolean;
  isPrivate: boolean;
  isReadonly: boolean;
  defaultValue?: string;
}

/**
 * 方法信息
 */
export interface MethodInfo {
  name: string;
  description: string;
  parameters: ParameterInfo[];
  returnType?: string;
  isAsync: boolean;
  isPrivate: boolean;
  isStatic: boolean;
}

/**
 * 解析用户描述并提取代码结构
 */
export function parseCodeDescription(
  description: string,
  type: GenerationType,
  language: ProgrammingLanguage
): CodeStructure {
  console.log(`🔍 Parsing description: "${description}" for ${type} in ${language}`);
  
  // 清理和标准化描述
  const cleanDescription = cleanDescription(description);
  
  // 提取主要名称
  const name = extractName(cleanDescription, type);
  
  // 提取参数信息
  const parameters = extractParameters(cleanDescription, language);
  
  // 提取返回类型
  const returnType = extractReturnType(cleanDescription, language);
  
  // 提取属性信息
  const properties = extractProperties(cleanDescription, language);
  
  // 提取方法信息
  const methods = extractMethods(cleanDescription, language);
  
  // 检测是否异步
  const isAsync = detectAsync(cleanDescription);
  
  // 提取依赖项
  const dependencies = extractDependencies(cleanDescription);
  
  // 评估复杂度
  const complexity = assessComplexity(cleanDescription, parameters, properties, methods);
  
  const result: CodeStructure = {
    name,
    description: cleanDescription,
    parameters,
    returnType,
    properties,
    methods,
    isAsync,
    dependencies,
    complexity
  };
  
  console.log(`✅ Parsed structure: ${name} (${complexity} complexity)`);
  return result;
}

/**
 * 清理描述文本
 */
function cleanDescription(description: string): string {
  return description
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s\u4e00-\u9fff.,()[\]{}:;!?-]/g, '');
}

/**
 * 提取主要名称
 */
function extractName(description: string, type: GenerationType): string {
  const lowerDesc = description.toLowerCase();
  
  // 常见的命名模式
  const patterns = [
    // 直接命名模式
    /(?:创建|生成|实现|编写)\s*(?:一个|个)?\s*([a-zA-Z_$][a-zA-Z0-9_$]*)/,
    /(?:create|generate|implement|write)\s*(?:a|an)?\s*([a-zA-Z_$][a-zA-Z0-9_$]*)/i,
    
    // 功能描述模式
    /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?:函数|方法|类|组件|模块)/,
    /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?:function|method|class|component|module)/i,
    
    // 动作模式
    /(?:用于|用来|for)\s*([a-zA-Z_$][a-zA-Z0-9_$]*)/,
    
    // 中文功能描述
    /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*功能/,
    /([\u4e00-\u9fff]+)(?:函数|方法|类|组件|模块)/
  ];
  
  for (const pattern of patterns) {
    const match = description.match(pattern);
    if (match && match[1]) {
      let name = match[1];
      
      // 如果是中文，转换为英文
      if (/[\u4e00-\u9fff]/.test(name)) {
        name = translateChineseToEnglish(name, type);
      }
      
      // 格式化名称
      return formatName(name, type);
    }
  }
  
  // 如果没有找到，生成默认名称
  return generateDefaultName(type);
}

/**
 * 提取参数信息
 */
function extractParameters(description: string, language: ProgrammingLanguage): ParameterInfo[] {
  const parameters: ParameterInfo[] = [];
  
  // 参数模式匹配
  const paramPatterns = [
    // 接受/接收参数模式
    /(?:接受|接收|传入|参数)\s*([^，。,.\n]+)/g,
    /(?:accepts?|receives?|takes?|parameters?)\s*([^,..\n]+)/gi,
    
    // 括号参数模式
    /\(([^)]+)\)/g,
    
    // 具体参数描述
    /(\w+)\s*(?:参数|parameter)\s*[:：]\s*([^，。,.\n]+)/g
  ];
  
  for (const pattern of paramPatterns) {
    let match;
    while ((match = pattern.exec(description)) !== null) {
      const paramText = match[1].trim();
      const parsedParams = parseParameterText(paramText, language);
      parameters.push(...parsedParams);
    }
  }
  
  // 去重
  const uniqueParams = parameters.filter((param, index, arr) => 
    arr.findIndex(p => p.name === param.name) === index
  );
  
  return uniqueParams;
}

/**
 * 解析参数文本
 */
function parseParameterText(text: string, language: ProgrammingLanguage): ParameterInfo[] {
  const parameters: ParameterInfo[] = [];
  
  // 分割多个参数
  const paramParts = text.split(/[，,和and&]/).map(p => p.trim());
  
  for (const part of paramParts) {
    if (!part) continue;
    
    // 提取参数名和类型
    const paramInfo = parseParameterPart(part, language);
    if (paramInfo) {
      parameters.push(paramInfo);
    }
  }
  
  return parameters;
}

/**
 * 解析单个参数部分
 */
function parseParameterPart(part: string, language: ProgrammingLanguage): ParameterInfo | null {
  // 类型模式匹配
  const typePatterns = [
    /(\w+)\s*[:：]\s*(\w+)/,  // name: type
    /(\w+)\s+(\w+)/,          // type name
    /(\w+)/                   // just name
  ];
  
  for (const pattern of typePatterns) {
    const match = part.match(pattern);
    if (match) {
      let name: string;
      let type: string;
      
      if (match.length === 3) {
        // name: type 或 type name
        if (part.includes(':') || part.includes('：')) {
          name = match[1];
          type = inferType(match[2], language);
        } else {
          type = inferType(match[1], language);
          name = match[2];
        }
      } else {
        // just name
        name = match[1];
        type = inferType(part, language);
      }
      
      return {
        name: formatVariableName(name),
        type,
        description: part,
        isOptional: part.includes('可选') || part.includes('optional'),
        defaultValue: extractDefaultValue(part)
      };
    }
  }
  
  return null;
}

/**
 * 提取返回类型
 */
function extractReturnType(description: string, language: ProgrammingLanguage): string | undefined {
  const returnPatterns = [
    /返回\s*([^，。,.\n]+)/,
    /return[s]?\s*([^,..\n]+)/i,
    /输出\s*([^，。,.\n]+)/,
    /output[s]?\s*([^,..\n]+)/i
  ];
  
  for (const pattern of returnPatterns) {
    const match = description.match(pattern);
    if (match && match[1]) {
      return inferType(match[1].trim(), language);
    }
  }
  
  return undefined;
}

/**
 * 提取属性信息
 */
function extractProperties(description: string, language: ProgrammingLanguage): PropertyInfo[] {
  const properties: PropertyInfo[] = [];
  
  const propertyPatterns = [
    /(?:属性|property|field)\s*([^，。,.\n]+)/gi,
    /(?:包含|contains?|has)\s*([^，。,.\n]+)/gi
  ];
  
  for (const pattern of propertyPatterns) {
    let match;
    while ((match = pattern.exec(description)) !== null) {
      const propText = match[1].trim();
      const parsedProps = parsePropertyText(propText, language);
      properties.push(...parsedProps);
    }
  }
  
  return properties;
}

/**
 * 解析属性文本
 */
function parsePropertyText(text: string, language: ProgrammingLanguage): PropertyInfo[] {
  const properties: PropertyInfo[] = [];
  
  const propParts = text.split(/[，,和and&]/).map(p => p.trim());
  
  for (const part of propParts) {
    if (!part) continue;
    
    const propInfo = parsePropertyPart(part, language);
    if (propInfo) {
      properties.push(propInfo);
    }
  }
  
  return properties;
}

/**
 * 解析单个属性部分
 */
function parsePropertyPart(part: string, language: ProgrammingLanguage): PropertyInfo | null {
  const match = part.match(/(\w+)/);
  if (match) {
    return {
      name: formatVariableName(match[1]),
      type: inferType(part, language),
      description: part,
      isOptional: part.includes('可选') || part.includes('optional'),
      isPrivate: part.includes('私有') || part.includes('private'),
      isReadonly: part.includes('只读') || part.includes('readonly'),
      defaultValue: extractDefaultValue(part)
    };
  }
  
  return null;
}

/**
 * 提取方法信息
 */
function extractMethods(description: string, language: ProgrammingLanguage): MethodInfo[] {
  const methods: MethodInfo[] = [];
  
  const methodPatterns = [
    /(?:方法|method)\s*([^，。,.\n]+)/gi,
    /(?:可以|能够|can)\s*([^，。,.\n]+)/gi
  ];
  
  for (const pattern of methodPatterns) {
    let match;
    while ((match = pattern.exec(description)) !== null) {
      const methodText = match[1].trim();
      const methodInfo = parseMethodText(methodText, language);
      if (methodInfo) {
        methods.push(methodInfo);
      }
    }
  }
  
  return methods;
}

/**
 * 解析方法文本
 */
function parseMethodText(text: string, language: ProgrammingLanguage): MethodInfo | null {
  const match = text.match(/(\w+)/);
  if (match) {
    return {
      name: formatMethodName(match[1]),
      description: text,
      parameters: [],
      returnType: inferType(text, language),
      isAsync: detectAsync(text),
      isPrivate: text.includes('私有') || text.includes('private'),
      isStatic: text.includes('静态') || text.includes('static')
    };
  }
  
  return null;
}

/**
 * 检测是否异步
 */
function detectAsync(description: string): boolean {
  const asyncKeywords = [
    '异步', 'async', 'await', '等待', 'promise', 'callback',
    '回调', '网络请求', 'api', 'fetch', 'http', 'ajax'
  ];
  
  const lowerDesc = description.toLowerCase();
  return asyncKeywords.some(keyword => lowerDesc.includes(keyword.toLowerCase()));
}

/**
 * 提取依赖项
 */
function extractDependencies(description: string): string[] {
  const dependencies: string[] = [];
  
  const depPatterns = [
    /(?:使用|依赖|需要|require|import|use)\s*([a-zA-Z_$][a-zA-Z0-9_$]*)/gi,
    /(?:基于|based on)\s*([a-zA-Z_$][a-zA-Z0-9_$]*)/gi
  ];
  
  for (const pattern of depPatterns) {
    let match;
    while ((match = pattern.exec(description)) !== null) {
      if (match[1] && !dependencies.includes(match[1])) {
        dependencies.push(match[1]);
      }
    }
  }
  
  return dependencies;
}

/**
 * 评估复杂度
 */
function assessComplexity(
  description: string,
  parameters: ParameterInfo[],
  properties: PropertyInfo[],
  methods: MethodInfo[]
): "simple" | "medium" | "complex" {
  let score = 0;
  
  // 基于描述长度
  score += Math.min(description.length / 50, 3);
  
  // 基于参数数量
  score += parameters.length * 0.5;
  
  // 基于属性数量
  score += properties.length * 0.3;
  
  // 基于方法数量
  score += methods.length * 0.7;
  
  // 基于关键词
  const complexKeywords = ['算法', '优化', '缓存', '并发', '异步', '数据库', '网络'];
  const complexCount = complexKeywords.filter(keyword => 
    description.toLowerCase().includes(keyword)
  ).length;
  score += complexCount * 0.5;
  
  if (score <= 2) return "simple";
  if (score <= 5) return "medium";
  return "complex";
}

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 推断类型
 */
function inferType(text: string, language: ProgrammingLanguage): string {
  const lowerText = text.toLowerCase();
  
  // 数字类型
  if (lowerText.includes('数字') || lowerText.includes('number') || 
      lowerText.includes('整数') || lowerText.includes('int')) {
    return language === 'typescript' ? 'number' : 'int';
  }
  
  // 字符串类型
  if (lowerText.includes('字符串') || lowerText.includes('string') ||
      lowerText.includes('文本') || lowerText.includes('text')) {
    return 'string';
  }
  
  // 布尔类型
  if (lowerText.includes('布尔') || lowerText.includes('boolean') ||
      lowerText.includes('真假') || lowerText.includes('bool')) {
    return 'boolean';
  }
  
  // 数组类型
  if (lowerText.includes('数组') || lowerText.includes('array') ||
      lowerText.includes('列表') || lowerText.includes('list')) {
    return language === 'typescript' ? 'any[]' : 'Array';
  }
  
  // 对象类型
  if (lowerText.includes('对象') || lowerText.includes('object') ||
      lowerText.includes('obj')) {
    return language === 'typescript' ? 'object' : 'Object';
  }
  
  // 默认类型
  return language === 'typescript' ? 'any' : 'Object';
}

/**
 * 格式化名称
 */
function formatName(name: string, type: GenerationType): string {
  // 移除特殊字符
  name = name.replace(/[^\w]/g, '');
  
  switch (type) {
    case 'class':
    case 'component':
      return toPascalCase(name);
    case 'function':
    case 'method':
      return toCamelCase(name);
    default:
      return toCamelCase(name);
  }
}

/**
 * 格式化变量名
 */
function formatVariableName(name: string): string {
  return toCamelCase(name.replace(/[^\w]/g, ''));
}

/**
 * 格式化方法名
 */
function formatMethodName(name: string): string {
  return toCamelCase(name.replace(/[^\w]/g, ''));
}

/**
 * 转换为 PascalCase
 */
function toPascalCase(str: string): string {
  return str.replace(/(?:^|[^a-zA-Z0-9])([a-zA-Z0-9])/g, (_, char) => char.toUpperCase());
}

/**
 * 转换为 camelCase
 */
function toCamelCase(str: string): string {
  const pascal = toPascalCase(str);
  return pascal.charAt(0).toLowerCase() + pascal.slice(1);
}

/**
 * 中文转英文 (简化实现)
 */
function translateChineseToEnglish(chinese: string, type: GenerationType): string {
  const translations: Record<string, string> = {
    '用户': 'user',
    '登录': 'login',
    '注册': 'register',
    '密码': 'password',
    '邮箱': 'email',
    '验证': 'validate',
    '创建': 'create',
    '删除': 'delete',
    '更新': 'update',
    '查询': 'query',
    '搜索': 'search',
    '列表': 'list',
    '详情': 'detail',
    '配置': 'config',
    '设置': 'setting',
    '管理': 'manage',
    '处理': 'handle',
    '计算': 'calculate',
    '格式化': 'format',
    '转换': 'convert'
  };
  
  return translations[chinese] || 'generated';
}

/**
 * 提取默认值
 */
function extractDefaultValue(text: string): string | undefined {
  const defaultPatterns = [
    /默认\s*[:：]\s*([^，。,.\n]+)/,
    /default\s*[:：]\s*([^,..\n]+)/i
  ];
  
  for (const pattern of defaultPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return undefined;
}

/**
 * 生成默认名称
 */
function generateDefaultName(type: GenerationType): string {
  const defaults = {
    function: 'generatedFunction',
    class: 'GeneratedClass',
    module: 'generatedModule',
    component: 'GeneratedComponent',
    test: 'generatedTest',
    refactor: 'refactorSuggestion',
    optimization: 'optimizationSuggestion'
  };
  
  return defaults[type] || 'generated';
}
