/**
 * 智能代码生成引擎 - 核心生成逻辑
 * v1.3.0 核心引擎实现
 */

import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import { PredictionAnalysis } from "../prediction/types.js";
import {
  CodeGenerationRequest,
  CodeGenerationResult,
  GeneratedCode,
  QualityAssessment,
  GenerationSuggestion,
  GenerationStatistics,
  NextStep,
  GenerationType,
  ProgrammingLanguage
} from "./types.js";
import { extractProjectContext } from "./context.js";
import { getCodeTemplate, renderTemplate } from "./templates.js";
import { assessGeneratedCodeQuality } from "./quality.js";
import { parseCodeDescription, CodeStructure } from "./nlp.js";
import { generateRefactorSuggestions } from "./refactor.js";

/**
 * 智能代码生成引擎
 */
export class CodeGenerationEngine {
  private codebaseAnalysis: CodebaseAnalysis;
  private predictionAnalysis: PredictionAnalysis;

  constructor(codebaseAnalysis: CodebaseAnalysis, predictionAnalysis: PredictionAnalysis) {
    this.codebaseAnalysis = codebaseAnalysis;
    this.predictionAnalysis = predictionAnalysis;
  }

  /**
   * 生成代码
   */
  async generateCode(request: CodeGenerationRequest): Promise<CodeGenerationResult> {
    console.log(`🚀 Starting code generation: ${request.type} - ${request.description}`);

    const startTime = Date.now();

    try {
      // 1. 分析生成上下文
      const context = await this.analyzeGenerationContext(request);

      // 2. 选择生成策略
      const strategy = this.selectGenerationStrategy(request, context);

      // 3. 生成代码
      const generatedCode = await this.executeGeneration(request, context, strategy);

      // 4. 质量评估
      const qualityAssessment = await this.assessQuality(generatedCode, request);

      // 5. 生成建议
      const suggestions = await this.generateSuggestions(generatedCode, qualityAssessment, request);

      // 6. 计算统计信息
      const statistics = this.calculateStatistics(generatedCode, startTime);

      // 7. 生成后续步骤
      const nextSteps = this.generateNextSteps(generatedCode, qualityAssessment, request);

      console.log(`✅ Code generation completed in ${statistics.generationTime}ms`);

      return {
        generatedCode,
        qualityAssessment,
        suggestions,
        statistics,
        nextSteps
      };

    } catch (error) {
      console.error(`❌ Code generation failed:`, error);
      throw new Error(`Code generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析生成上下文
   */
  private async analyzeGenerationContext(request: CodeGenerationRequest) {
    console.log(`🔍 Analyzing generation context...`);

    // 提取项目上下文
    const projectContext = await extractProjectContext(
      this.codebaseAnalysis,
      request.context.projectRoot
    );

    // 分析相关文件
    const relatedFiles = this.findRelatedFiles(request);

    // 提取架构模式
    const architecturePatterns = this.extractArchitecturePatterns();

    // 分析依赖关系
    const dependencies = this.analyzeDependencies(request);

    return {
      ...request.context,
      projectContext,
      relatedFiles,
      architecturePatterns,
      dependencies
    };
  }

  /**
   * 选择生成策略
   */
  private selectGenerationStrategy(request: CodeGenerationRequest, context: any) {
    console.log(`🎯 Selecting generation strategy for ${request.type}...`);

    const strategies = {
      function: this.generateFunction.bind(this),
      class: this.generateClass.bind(this),
      module: this.generateModule.bind(this),
      component: this.generateComponent.bind(this),
      test: this.generateTest.bind(this),
      refactor: this.generateRefactor.bind(this),
      optimization: this.generateOptimization.bind(this)
    };

    return strategies[request.type] || strategies.function;
  }

  /**
   * 执行代码生成
   */
  private async executeGeneration(
    request: CodeGenerationRequest,
    context: any,
    strategy: Function
  ): Promise<GeneratedCode[]> {
    console.log(`⚡ Executing generation strategy...`);

    return await strategy(request, context);
  }

  /**
   * 生成函数
   */
  private async generateFunction(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🔧 Generating function: ${request.description}`);

    // 获取函数模板
    const template = await getCodeTemplate("function", request.language);

    // 分析函数需求
    const functionSpec = this.analyzeFunctionRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, functionSpec);

    // 生成测试（如果需要）
    const testCode = request.options.includeTests
      ? await this.generateFunctionTest(functionSpec, request.language)
      : null;

    const result: GeneratedCode[] = [{
      filePath: context.targetFile || `${functionSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "function",
      changeType: "create",
      dependencies: functionSpec.dependencies
    }];

    if (testCode) {
      result.push({
        filePath: `${functionSpec.name}.test.${this.getFileExtension(request.language)}`,
        content: testCode,
        type: "test",
        changeType: "create",
        dependencies: [...functionSpec.dependencies, "test-framework"]
      });
    }

    return result;
  }

  /**
   * 生成类
   */
  private async generateClass(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🏗️ Generating class: ${request.description}`);

    // 获取类模板
    const template = await getCodeTemplate("class", request.language);

    // 分析类需求
    const classSpec = this.analyzeClassRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, classSpec);

    return [{
      filePath: context.targetFile || `${classSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "class",
      changeType: "create",
      dependencies: classSpec.dependencies
    }];
  }

  /**
   * 生成模块
   */
  private async generateModule(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`📦 Generating module: ${request.description}`);

    // 分析模块需求
    const moduleSpec = this.analyzeModuleRequirements(request, context);

    const generatedFiles: GeneratedCode[] = [];

    // 生成主模块文件
    const mainTemplate = await getCodeTemplate("module", request.language);
    const mainCode = await renderTemplate(mainTemplate, moduleSpec);

    generatedFiles.push({
      filePath: `${moduleSpec.name}/index.${this.getFileExtension(request.language)}`,
      content: mainCode,
      type: "module",
      changeType: "create",
      dependencies: moduleSpec.dependencies
    });

    // 生成子组件
    for (const component of moduleSpec.components) {
      const componentCode = await this.generateComponent({
        ...request,
        description: component.description,
        type: "component"
      }, { ...context, targetFile: `${moduleSpec.name}/${component.name}.${this.getFileExtension(request.language)}` });

      generatedFiles.push(...componentCode);
    }

    return generatedFiles;
  }

  /**
   * 生成组件
   */
  private async generateComponent(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🧩 Generating component: ${request.description}`);

    // 根据语言选择组件类型
    const componentType = this.determineComponentType(request.language);
    const template = await getCodeTemplate(componentType, request.language);

    // 分析组件需求
    const componentSpec = this.analyzeComponentRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, componentSpec);

    return [{
      filePath: context.targetFile || `${componentSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "component",
      changeType: "create",
      dependencies: componentSpec.dependencies
    }];
  }

  /**
   * 生成测试
   */
  private async generateTest(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🧪 Generating tests: ${request.description}`);

    // 分析测试目标
    const testTarget = this.analyzeTestTarget(request, context);

    // 生成不同类型的测试
    const generatedTests: GeneratedCode[] = [];

    // 单元测试
    if (testTarget.needsUnitTests) {
      const unitTests = await this.generateUnitTests(testTarget, request.language);
      generatedTests.push(...unitTests);
    }

    // 集成测试
    if (testTarget.needsIntegrationTests) {
      const integrationTests = await this.generateIntegrationTests(testTarget, request.language);
      generatedTests.push(...integrationTests);
    }

    return generatedTests;
  }

  /**
   * 生成重构建议
   */
  private async generateRefactor(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🔄 Generating refactor suggestions: ${request.description}`);

    // 使用重构建议引擎生成建议
    const refactorSuggestions = await generateRefactorSuggestions(
      this.codebaseAnalysis,
      this.predictionAnalysis,
      context.targetFile
    );

    const generatedCode: GeneratedCode[] = [];

    for (const suggestion of refactorSuggestions) {
      generatedCode.push({
        filePath: suggestion.targetFile,
        content: this.formatRefactorSuggestion(suggestion),
        type: "refactor",
        changeType: "modify",
        dependencies: []
      });
    }

    return generatedCode;
  }

  /**
   * 格式化重构建议
   */
  private formatRefactorSuggestion(suggestion: any): string {
    return `# 重构建议: ${suggestion.description}

## 重构类型
${suggestion.type}

## 风险级别
${suggestion.risk}

## 重构前代码
\`\`\`typescript
${suggestion.beforeCode}
\`\`\`

## 重构后代码
\`\`\`typescript
${suggestion.afterCode}
\`\`\`

## 预期收益
${suggestion.benefits.map((benefit: string) => `- ${benefit}`).join('\n')}

## 影响分析
- 影响文件: ${suggestion.impact.affectedFiles.join(', ')}
- 破坏性变更: ${suggestion.impact.breakingChanges ? '是' : '否'}
- 测试影响: ${suggestion.impact.testImpact}
`;
  }

  /**
   * 生成性能优化
   */
  private async generateOptimization(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`⚡ Generating performance optimizations: ${request.description}`);

    // 基于性能预测分析生成优化建议
    const optimizationOpportunities = this.identifyOptimizationOpportunities(context);

    const optimizations: GeneratedCode[] = [];

    for (const opportunity of optimizationOpportunities) {
      const optimizedCode = await this.applyOptimization(opportunity, request.language);
      optimizations.push(optimizedCode);
    }

    return optimizations;
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  /**
   * 查找相关文件
   */
  private findRelatedFiles(request: CodeGenerationRequest): FileAnalysis[] {
    // 基于描述和上下文查找相关文件
    return this.codebaseAnalysis.files.filter(file =>
      this.isFileRelevant(file, request)
    );
  }

  /**
   * 判断文件是否相关
   */
  private isFileRelevant(file: FileAnalysis, request: CodeGenerationRequest): boolean {
    // 简单的相关性判断逻辑
    const keywords = request.description.toLowerCase().split(' ');
    const fileName = file.path.toLowerCase();

    return keywords.some(keyword => fileName.includes(keyword));
  }

  /**
   * 提取架构模式
   */
  private extractArchitecturePatterns(): string[] {
    // 从代码库分析中提取架构模式
    return this.codebaseAnalysis.architecture?.patterns || [];
  }

  /**
   * 分析依赖关系
   */
  private analyzeDependencies(request: CodeGenerationRequest) {
    // 分析项目依赖
    return this.codebaseAnalysis.dependencies || [];
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(language: ProgrammingLanguage): string {
    const extensions = {
      typescript: "ts",
      javascript: "js",
      python: "py",
      java: "java",
      go: "go",
      rust: "rs"
    };

    return extensions[language] || "txt";
  }

  /**
   * 分析函数需求
   */
  private analyzeFunctionRequirements(request: CodeGenerationRequest, context: any) {
    // 使用 NLP 处理从描述中提取函数规格
    const codeStructure = parseCodeDescription(request.description, request.type, request.language);

    return {
      name: codeStructure.name,
      parameters: codeStructure.parameters,
      returnType: codeStructure.returnType || "void",
      dependencies: codeStructure.dependencies,
      documentation: request.options.includeComments,
      isAsync: codeStructure.isAsync,
      complexity: codeStructure.complexity
    };
  }

  /**
   * 分析类需求
   */
  private analyzeClassRequirements(request: CodeGenerationRequest, context: any) {
    const codeStructure = parseCodeDescription(request.description, request.type, request.language);

    return {
      name: codeStructure.name,
      properties: codeStructure.properties,
      methods: codeStructure.methods,
      inheritance: null, // 暂时简化
      dependencies: codeStructure.dependencies,
      hasConstructor: codeStructure.properties.length > 0,
      constructorParams: codeStructure.properties.map(prop => ({
        name: prop.name,
        type: prop.type,
        description: prop.description
      }))
    };
  }

  /**
   * 分析模块需求
   */
  private analyzeModuleRequirements(request: CodeGenerationRequest, context: any) {
    return {
      name: this.extractModuleName(request.description),
      components: this.extractComponents(request.description),
      exports: this.extractExports(request.description),
      dependencies: this.extractDependencies(request.description, context)
    };
  }

  /**
   * 分析组件需求
   */
  private analyzeComponentRequirements(request: CodeGenerationRequest, context: any) {
    return {
      name: this.extractComponentName(request.description),
      props: this.extractProps(request.description),
      state: this.extractState(request.description),
      methods: this.extractMethods(request.description),
      dependencies: this.extractDependencies(request.description, context)
    };
  }

  // 这些提取方法需要实现具体的NLP逻辑
  private extractFunctionName(description: string): string {
    // 简单实现，实际需要更复杂的NLP
    const match = description.match(/function\s+(\w+)/i) || description.match(/(\w+)\s+function/i);
    return match ? match[1] : "generatedFunction";
  }

  private extractParameters(description: string): any[] {
    // 简单实现
    return [];
  }

  private extractReturnType(description: string): string {
    // 简单实现
    return "any";
  }

  private extractDependencies(description: string, context: any): string[] {
    // 简单实现
    return [];
  }

  private extractClassName(description: string): string {
    const match = description.match(/class\s+(\w+)/i) || description.match(/(\w+)\s+class/i);
    return match ? match[1] : "GeneratedClass";
  }

  private extractProperties(description: string): any[] {
    return [];
  }

  private extractMethods(description: string): any[] {
    return [];
  }

  private extractInheritance(description: string, context: any): string | null {
    return null;
  }

  private extractModuleName(description: string): string {
    const match = description.match(/module\s+(\w+)/i) || description.match(/(\w+)\s+module/i);
    return match ? match[1] : "generatedModule";
  }

  private extractComponents(description: string): any[] {
    return [];
  }

  private extractExports(description: string): any[] {
    return [];
  }

  private extractComponentName(description: string): string {
    const match = description.match(/component\s+(\w+)/i) || description.match(/(\w+)\s+component/i);
    return match ? match[1] : "GeneratedComponent";
  }

  private extractProps(description: string): any[] {
    return [];
  }

  private extractState(description: string): any[] {
    return [];
  }

  private determineComponentType(language: ProgrammingLanguage): string {
    if (language === "typescript" || language === "javascript") {
      return "react-component"; // 或 "vue-component"
    }
    return "component";
  }

  private async generateFunctionTest(functionSpec: any, language: ProgrammingLanguage): Promise<string> {
    const template = await getCodeTemplate("function-test", language);
    return await renderTemplate(template, functionSpec);
  }

  private analyzeTestTarget(request: CodeGenerationRequest, context: any) {
    return {
      targetFile: context.targetFile,
      needsUnitTests: true,
      needsIntegrationTests: false,
      testFramework: "jest" // 默认
    };
  }

  private async generateUnitTests(testTarget: any, language: ProgrammingLanguage): Promise<GeneratedCode[]> {
    // 实现单元测试生成
    return [];
  }

  private async generateIntegrationTests(testTarget: any, language: ProgrammingLanguage): Promise<GeneratedCode[]> {
    // 实现集成测试生成
    return [];
  }

  private identifyRefactorOpportunities(context: any): any[] {
    // 基于预测分析识别重构机会
    return [];
  }

  private async applyRefactoring(opportunity: any, language: ProgrammingLanguage): Promise<GeneratedCode> {
    // 应用重构
    return {
      filePath: opportunity.filePath,
      content: opportunity.refactoredCode,
      type: "refactor",
      changeType: "modify",
      dependencies: []
    };
  }

  private identifyOptimizationOpportunities(context: any): any[] {
    // 基于性能预测识别优化机会
    return [];
  }

  private async applyOptimization(opportunity: any, language: ProgrammingLanguage): Promise<GeneratedCode> {
    // 应用性能优化
    return {
      filePath: opportunity.filePath,
      content: opportunity.optimizedCode,
      type: "optimization",
      changeType: "modify",
      dependencies: []
    };
  }

  private async assessQuality(generatedCode: GeneratedCode[], request: CodeGenerationRequest): Promise<QualityAssessment> {
    return await assessGeneratedCodeQuality(generatedCode, this.codebaseAnalysis);
  }

  private async generateSuggestions(
    generatedCode: GeneratedCode[],
    qualityAssessment: QualityAssessment,
    request: CodeGenerationRequest
  ): Promise<GenerationSuggestion[]> {
    const suggestions: GenerationSuggestion[] = [];

    // 基于质量评估生成建议
    if (qualityAssessment.overallScore < 0.8) {
      suggestions.push({
        type: "improvement",
        message: "生成的代码质量可以进一步改进",
        affectedFiles: generatedCode.map(code => code.filePath),
        severity: "medium",
        fixSuggestion: "考虑重构以提高代码质量"
      });
    }

    return suggestions;
  }

  private calculateStatistics(generatedCode: GeneratedCode[], startTime: number): GenerationStatistics {
    const endTime = Date.now();
    const totalLines = generatedCode.reduce((sum, code) =>
      sum + code.content.split('\n').length, 0
    );

    return {
      filesGenerated: generatedCode.length,
      linesGenerated: totalLines,
      generationTime: endTime - startTime,
      templatesUsed: [], // 需要跟踪使用的模板
      patternsApplied: [] // 需要跟踪应用的模式
    };
  }

  private generateNextSteps(
    generatedCode: GeneratedCode[],
    qualityAssessment: QualityAssessment,
    request: CodeGenerationRequest
  ): NextStep[] {
    const nextSteps: NextStep[] = [];

    // 总是建议测试
    nextSteps.push({
      description: "运行生成的测试以验证代码功能",
      type: "test",
      priority: "high",
      estimatedTime: "5-10 分钟",
      relatedTools: ["generate-tests"]
    });

    // 如果质量不够高，建议审查
    if (qualityAssessment.overallScore < 0.8) {
      nextSteps.push({
        description: "审查生成的代码并进行必要的改进",
        type: "review",
        priority: "medium",
        estimatedTime: "15-30 分钟",
        relatedTools: ["suggest-refactor"]
      });
    }

    return nextSteps;
  }
}
