/**
 * 智能代码生成 - 类型定义
 * v1.3.0 核心类型系统
 */

import { CodebaseAnalysis, FileAnalysis, FunctionInfo, ClassInfo } from "../analysis/types.js";
import { PredictionAnalysis } from "../prediction/types.js";

// ============================================================================
// 核心生成类型
// ============================================================================

/**
 * 代码生成请求
 */
export interface CodeGenerationRequest {
  /** 生成类型 */
  type: GenerationType;
  /** 目标语言 */
  language: ProgrammingLanguage;
  /** 生成描述 */
  description: string;
  /** 上下文信息 */
  context: GenerationContext;
  /** 生成选项 */
  options: GenerationOptions;
}

/**
 * 生成类型枚举
 */
export type GenerationType = 
  | "function"      // 函数生成
  | "class"         // 类生成
  | "module"        // 模块生成
  | "component"     // 组件生成
  | "test"          // 测试生成
  | "refactor"      // 重构建议
  | "optimization"; // 性能优化

/**
 * 支持的编程语言
 */
export type ProgrammingLanguage = 
  | "typescript" 
  | "javascript" 
  | "python" 
  | "java" 
  | "go" 
  | "rust";

/**
 * 生成上下文
 */
export interface GenerationContext {
  /** 项目根路径 */
  projectRoot: string;
  /** 目标文件路径 */
  targetFile?: string;
  /** 相关文件分析 */
  relatedFiles: FileAnalysis[];
  /** 项目风格指南 */
  styleGuide: ProjectStyleGuide;
  /** 架构模式 */
  architecturePatterns: string[];
  /** 依赖信息 */
  dependencies: DependencyInfo[];
}

/**
 * 生成选项
 */
export interface GenerationOptions {
  /** 输出格式 */
  outputFormat: "code" | "diff" | "suggestion";
  /** 包含注释 */
  includeComments: boolean;
  /** 包含类型注解 */
  includeTypes: boolean;
  /** 包含测试 */
  includeTests: boolean;
  /** 质量级别 */
  qualityLevel: "basic" | "standard" | "premium";
  /** 风格偏好 */
  stylePreference: "conservative" | "modern" | "aggressive";
}

// ============================================================================
// 项目风格和模式
// ============================================================================

/**
 * 项目风格指南
 */
export interface ProjectStyleGuide {
  /** 命名约定 */
  namingConventions: NamingConventions;
  /** 代码格式 */
  formatting: FormattingRules;
  /** 架构约定 */
  architecturalConventions: ArchitecturalConventions;
  /** 文档约定 */
  documentationConventions: DocumentationConventions;
}

/**
 * 命名约定
 */
export interface NamingConventions {
  /** 函数命名 */
  functions: "camelCase" | "snake_case" | "PascalCase";
  /** 变量命名 */
  variables: "camelCase" | "snake_case" | "UPPER_CASE";
  /** 类命名 */
  classes: "PascalCase" | "camelCase";
  /** 文件命名 */
  files: "camelCase" | "kebab-case" | "snake_case";
  /** 常量命名 */
  constants: "UPPER_CASE" | "camelCase";
}

/**
 * 格式化规则
 */
export interface FormattingRules {
  /** 缩进类型 */
  indentation: "spaces" | "tabs";
  /** 缩进大小 */
  indentSize: number;
  /** 行长度限制 */
  lineLength: number;
  /** 分号使用 */
  semicolons: "always" | "never" | "asi";
  /** 引号类型 */
  quotes: "single" | "double" | "backtick";
}

/**
 * 架构约定
 */
export interface ArchitecturalConventions {
  /** 目录结构模式 */
  directoryStructure: string[];
  /** 模块导入模式 */
  importPatterns: string[];
  /** 错误处理模式 */
  errorHandlingPatterns: string[];
  /** 异步处理模式 */
  asyncPatterns: string[];
}

/**
 * 文档约定
 */
export interface DocumentationConventions {
  /** 注释风格 */
  commentStyle: "jsdoc" | "inline" | "block";
  /** 文档覆盖率要求 */
  documentationCoverage: "minimal" | "standard" | "comprehensive";
  /** README 模板 */
  readmeTemplate?: string;
}

// ============================================================================
// 代码生成结果
// ============================================================================

/**
 * 代码生成结果
 */
export interface CodeGenerationResult {
  /** 生成的代码 */
  generatedCode: GeneratedCode[];
  /** 质量评估 */
  qualityAssessment: QualityAssessment;
  /** 建议和警告 */
  suggestions: GenerationSuggestion[];
  /** 生成统计 */
  statistics: GenerationStatistics;
  /** 后续步骤 */
  nextSteps: NextStep[];
}

/**
 * 生成的代码
 */
export interface GeneratedCode {
  /** 文件路径 */
  filePath: string;
  /** 代码内容 */
  content: string;
  /** 代码类型 */
  type: GenerationType;
  /** 变更类型 */
  changeType: "create" | "modify" | "delete";
  /** 行号范围 */
  lineRange?: [number, number];
  /** 相关依赖 */
  dependencies: string[];
}

/**
 * 质量评估
 */
export interface QualityAssessment {
  /** 整体评分 */
  overallScore: number;
  /** 各维度评分 */
  dimensions: {
    readability: number;
    maintainability: number;
    performance: number;
    security: number;
    testability: number;
  };
  /** 质量等级 */
  grade: "A" | "B" | "C" | "D" | "F";
  /** 改进建议 */
  improvements: string[];
}

/**
 * 生成建议
 */
export interface GenerationSuggestion {
  /** 建议类型 */
  type: "improvement" | "warning" | "error" | "optimization";
  /** 建议内容 */
  message: string;
  /** 影响文件 */
  affectedFiles: string[];
  /** 严重程度 */
  severity: "low" | "medium" | "high" | "critical";
  /** 修复建议 */
  fixSuggestion?: string;
}

/**
 * 生成统计
 */
export interface GenerationStatistics {
  /** 生成的文件数 */
  filesGenerated: number;
  /** 生成的代码行数 */
  linesGenerated: number;
  /** 生成时间 */
  generationTime: number;
  /** 模板使用情况 */
  templatesUsed: string[];
  /** 模式应用情况 */
  patternsApplied: string[];
}

/**
 * 后续步骤
 */
export interface NextStep {
  /** 步骤描述 */
  description: string;
  /** 步骤类型 */
  type: "test" | "review" | "refactor" | "deploy" | "document";
  /** 优先级 */
  priority: "low" | "medium" | "high" | "urgent";
  /** 预估时间 */
  estimatedTime: string;
  /** 相关工具 */
  relatedTools: string[];
}

// ============================================================================
// 重构相关类型
// ============================================================================

/**
 * 重构建议
 */
export interface RefactorSuggestion {
  /** 重构类型 */
  type: RefactorType;
  /** 目标文件 */
  targetFile: string;
  /** 重构描述 */
  description: string;
  /** 重构前代码 */
  beforeCode: string;
  /** 重构后代码 */
  afterCode: string;
  /** 影响分析 */
  impact: RefactorImpact;
  /** 风险评估 */
  risk: RiskLevel;
  /** 预期收益 */
  benefits: string[];
}

/**
 * 重构类型
 */
export type RefactorType = 
  | "extract_method"      // 提取方法
  | "extract_class"       // 提取类
  | "rename"              // 重命名
  | "move_method"         // 移动方法
  | "inline_method"       // 内联方法
  | "replace_conditional" // 替换条件表达式
  | "introduce_parameter" // 引入参数
  | "remove_parameter"    // 移除参数
  | "split_variable"      // 分解变量
  | "consolidate_duplicate"; // 合并重复代码

/**
 * 重构影响
 */
export interface RefactorImpact {
  /** 影响的文件 */
  affectedFiles: string[];
  /** 影响的函数 */
  affectedFunctions: string[];
  /** 影响的类 */
  affectedClasses: string[];
  /** 破坏性变更 */
  breakingChanges: boolean;
  /** 测试影响 */
  testImpact: "none" | "minor" | "major" | "complete_rewrite";
}

/**
 * 风险级别
 */
export type RiskLevel = "low" | "medium" | "high" | "critical";

// ============================================================================
// 测试生成相关类型
// ============================================================================

/**
 * 测试生成配置
 */
export interface TestGenerationConfig {
  /** 测试框架 */
  framework: TestFramework;
  /** 测试类型 */
  testTypes: TestType[];
  /** 覆盖率目标 */
  coverageTarget: number;
  /** 模拟策略 */
  mockingStrategy: MockingStrategy;
  /** 测试数据生成 */
  testDataGeneration: TestDataGeneration;
}

/**
 * 测试框架
 */
export type TestFramework = 
  | "jest" 
  | "mocha" 
  | "vitest" 
  | "jasmine" 
  | "cypress" 
  | "playwright";

/**
 * 测试类型
 */
export type TestType = 
  | "unit"        // 单元测试
  | "integration" // 集成测试
  | "e2e"         // 端到端测试
  | "performance" // 性能测试
  | "security"    // 安全测试
  | "accessibility"; // 可访问性测试

/**
 * 模拟策略
 */
export type MockingStrategy = 
  | "none"      // 不使用模拟
  | "minimal"   // 最小模拟
  | "standard"  // 标准模拟
  | "comprehensive"; // 全面模拟

/**
 * 测试数据生成
 */
export interface TestDataGeneration {
  /** 生成策略 */
  strategy: "random" | "realistic" | "edge_cases" | "comprehensive";
  /** 数据类型 */
  dataTypes: string[];
  /** 边界值测试 */
  boundaryTesting: boolean;
  /** 异常情况测试 */
  exceptionTesting: boolean;
}

// ============================================================================
// 性能优化相关类型
// ============================================================================

/**
 * 性能优化建议
 */
export interface PerformanceOptimization {
  /** 优化类型 */
  type: OptimizationType;
  /** 目标文件 */
  targetFile: string;
  /** 问题描述 */
  problemDescription: string;
  /** 优化前代码 */
  beforeCode: string;
  /** 优化后代码 */
  afterCode: string;
  /** 预期改进 */
  expectedImprovement: PerformanceImprovement;
  /** 实施复杂度 */
  implementationComplexity: "low" | "medium" | "high";
  /** 风险评估 */
  risks: string[];
}

/**
 * 优化类型
 */
export type OptimizationType = 
  | "algorithm"     // 算法优化
  | "data_structure" // 数据结构优化
  | "caching"       // 缓存优化
  | "async"         // 异步优化
  | "memory"        // 内存优化
  | "io"            // I/O 优化
  | "database"      // 数据库优化
  | "network";      // 网络优化

/**
 * 性能改进
 */
export interface PerformanceImprovement {
  /** 时间复杂度改进 */
  timeComplexity?: {
    before: string;
    after: string;
  };
  /** 空间复杂度改进 */
  spaceComplexity?: {
    before: string;
    after: string;
  };
  /** 执行时间改进 */
  executionTime?: {
    improvement: number; // 百分比
    unit: "ms" | "s" | "min";
  };
  /** 内存使用改进 */
  memoryUsage?: {
    improvement: number; // 百分比
    unit: "KB" | "MB" | "GB";
  };
  /** 吞吐量改进 */
  throughput?: {
    improvement: number; // 百分比
    unit: "req/s" | "ops/s" | "MB/s";
  };
}

// ============================================================================
// 辅助类型
// ============================================================================

/**
 * 依赖信息
 */
export interface DependencyInfo {
  /** 依赖名称 */
  name: string;
  /** 依赖版本 */
  version: string;
  /** 依赖类型 */
  type: "production" | "development" | "peer" | "optional";
  /** 使用情况 */
  usage: string[];
}

/**
 * 代码模板
 */
export interface CodeTemplate {
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板内容 */
  template: string;
  /** 模板变量 */
  variables: TemplateVariable[];
  /** 适用语言 */
  languages: ProgrammingLanguage[];
  /** 适用场景 */
  scenarios: GenerationType[];
}

/**
 * 模板变量
 */
export interface TemplateVariable {
  /** 变量名 */
  name: string;
  /** 变量类型 */
  type: "string" | "number" | "boolean" | "array" | "object";
  /** 默认值 */
  defaultValue?: any;
  /** 是否必需 */
  required: boolean;
  /** 变量描述 */
  description: string;
}
