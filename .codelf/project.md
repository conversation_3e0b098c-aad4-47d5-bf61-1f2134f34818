## Codelf

> 项目使用的技术、工具库及对应依赖版本如下：
> TypeScript（v5.7.3）、Node.js、@modelcontextprotocol/sdk（v1.5.0）、Zod（v3.24.2）

## 项目结构

> 文件级别的分析对于理解项目至关重要。

> 以下是项目的目录结构，并对重要部分进行了注释说明。

root

- .codelf // 项目文档目录，存放项目相关的说明文档
  ├── steering/ // Steering 文档目录，持久化项目知识
  │ ├── product.md // 产品概述文档
  │ ├── tech.md // 技术栈文档
  │ └── structure.md // 项目结构文档
  ├── workflows/ // 工作流程目录
  │ ├── specs/ // 规范工作流目录
  │ ├── bugs/ // Bug 修复工作流目录
  │ └── config.json // 工作流程配置文件
  ├── templates/ // 模板文件目录
  └── \*.md // 项目文档文件
- .git // Git 版本控制目录
- .gitignore // Git 忽略文件配置
- src/ // 源代码目录
  ├── analysis/ // 代码分析模块
  ├── prediction/ // 预测分析模块
  ├── generation/ // 智能代码生成模块 🆕
  │ ├── engine.ts // 核心生成引擎
  │ ├── types.ts // 生成相关类型定义 (100+ 接口)
  │ ├── context.ts // 上下文分析器
  │ ├── templates.ts // 代码模板管理系统 (12+ 模板)
  │ ├── quality.ts // 代码质量检查器
  │ ├── nlp.ts // NLP 处理模块 🆕
  │ └── refactor.ts // 重构建议引擎 🆕
  ├── tools/ // 工具模块
  │ ├── basic.ts // 基础工具
  │ ├── steering.ts // Steering 工具
  │ ├── spec.ts // 规范工具
  │ ├── bug.ts // Bug 工具
  │ ├── intelligence.ts // 智能分析工具
  │ ├── prediction.ts // 预测分析工具
  │ └── generation.ts // 代码生成工具 🆕
  └── server.ts // MCP 服务器配置
- index.ts // 主入口文件，包含 MCP 服务器实现和工具函数定义
- node_modules // Node.js 依赖包目录
- package.json // 项目配置文件，定义依赖和脚本
- pnpm-lock.yaml // pnpm 包管理器锁定文件
- README.md // 英文版项目说明文档
- README_CN.md // 中文版项目说明文档
- tsconfig.json // TypeScript 配置文件
- WORKFLOW_README.md // 工作流程说明文档
- V1_3_0_IMPLEMENTATION_PLAN.md // v1.3.0 实施计划 🆕
- V1_3_0_KICKOFF_REPORT.md // v1.3.0 启动报告 🆕
- V1_3_0_WEEK1_COMPLETION_REPORT.md // Week 1 完成报告 🆕

### 核心功能

该项目是一个 MCP（Model Context Protocol）服务器实现，现已扩展为完整的自动化工作流程系统，提供以下工具功能：

#### 基础工具

1. `get-project-info` - 获取项目详细信息，帮助 AI 更好地理解代码
2. `update-project-info` - 更新项目信息，维护.codelf 目录下的文档
3. `init-codelf` - 初始化.codelf 目录和文件，帮助建立项目文档结构

#### Steering 系统（持久化项目知识）

4. `init-steering` - 初始化 Steering 文档系统，创建产品概述、技术栈和项目结构文档
5. `get-steering` - 获取 Steering 文档内容，用于指导工作流程

#### 规范工作流（Spec Workflow）

6. `spec-create` - 创建新的规范工作流
7. `spec-requirements` - 为当前规范生成需求文档
8. `spec-status` - 查看规范的当前状态
9. `spec-list` - 列出所有规范

#### Bug 修复工作流（Bug Fix Workflow）

10. `bug-create` - 创建新的 Bug 修复工作流
11. `bug-status` - 查看 Bug 的当前状态
12. `bug-analyze` - Bug 根因分析
13. `bug-fix` - 实施 Bug 修复
14. `bug-verify` - 验证 Bug 修复

#### 智能分析系统（Intelligence Analysis）

15. `analyze-codebase` - 综合代码库分析
16. `map-dependencies` - 依赖关系映射和可视化
17. `assess-architecture` - 架构评估和建议
18. `track-evolution` - 项目演进追踪

#### 预测分析系统（Prediction Analysis）

19. `predict-issues` - AI 驱动问题预测
20. `assess-performance` - 性能风险评估
21. `scan-security` - 智能安全扫描
22. `measure-debt` - 技术债务量化

#### 智能代码生成系统（Code Generation）🆕

23. `generate-code` - 智能代码生成
24. `suggest-refactor` - 智能重构建议
25. `generate-tests` - 自动测试生成
26. `optimize-performance` - 性能优化建议

### 工作流程特性

#### 🎯 规范驱动开发流程

- **需求分析** → **设计文档** → **任务分解** → **实施执行**
- 集成 Steering 文档确保项目标准一致性
- 自动生成结构化文档和模板

#### 🐛 Bug 修复工作流程

- **问题报告** → **根因分析** → **修复实施** → **验证测试**
- 支持严重程度分级（low/medium/high/critical）
- 系统化的问题跟踪和状态管理

#### 📋 Steering 文档系统

- **product.md** - 产品愿景、目标用户、核心功能
- **tech.md** - 技术栈、开发工具、技术约束
- **structure.md** - 文件组织、命名约定、代码组织原则

#### 🧠 智能分析系统（v1.1.0）

- **代码语义分析** - TypeScript AST 解析，函数/类结构分析
- **依赖关系映射** - 模块依赖图谱，循环依赖检测
- **架构洞察** - 设计模式识别，SOLID 原则检查
- **质量评估** - 综合质量评分，改进建议生成
- **可视化输出** - Mermaid 图表，质量仪表板

#### 🔮 预测分析系统（v1.2.0）

- **问题预测分析** - 8 种问题类型预测，时间框架预测
- **性能风险评估** - 瓶颈识别，可扩展性问题预测
- **安全漏洞扫描** - 7 类漏洞检测，风险量化评估
- **技术债务量化** - 5 类债务分析，优先级排序
- **预防策略生成** - 具体可执行的行动计划

#### 🚀 智能代码生成系统（v1.3.0 新增）🆕

- **上下文感知生成** - 理解项目风格、架构模式、业务逻辑
- **智能重构建议** - 基于预测分析的重构优化
- **自动测试生成** - 单元测试、集成测试、性能测试
- **性能优化建议** - 算法优化、缓存策略、异步处理
- **质量保证生成** - 生成符合项目标准的高质量代码

项目通过 Node.js 实现，使用 TypeScript 进行开发，现已进化为具备完整 AI 编程能力的智能系统：从代码理解、问题预测到主动生成解决方案，为 AI 代码助手提供全方位的编程支持能力。
