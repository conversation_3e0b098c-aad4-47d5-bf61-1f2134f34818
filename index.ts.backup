#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { exec } from "child_process";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { promisify } from "util";
import { z } from "zod";
import * as gitignoreParser from "gitignore-parser";

// 工作流程相关类型定义
interface SpecConfig {
  name: string;
  title: string;
  status: 'created' | 'requirements' | 'design' | 'tasks' | 'implementing' | 'completed';
  createdAt: string;
  updatedAt: string;
  currentTask?: number;
}

interface BugConfig {
  name: string;
  title: string;
  status: 'reported' | 'analyzing' | 'fixing' | 'verifying' | 'resolved';
  createdAt: string;
  updatedAt: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface WorkflowConfig {
  specs: Record<string, SpecConfig>;
  bugs: Record<string, BugConfig>;
  version: string;
}

const execPromise = promisify(exec);
// 默认黑名单，当.gitignore不存在时使用
const folderBlackList = [
  "node_modules",
  ".codelf",
  ".git",
  ".idea",
  ".vscode",
  "dist",
  "build",
  "out",
  "target",
  "bin",
  "obj",
  ".next",
  "coverage",
  "__pycache__",
  ".DS_Store",
  "tmp",
  "temp",
  "logs",
  ".cache",
  ".github",
  ".gitlab",
  "vendor",
];

const forceBlackList = [".git", ".codelf", ".vscode", ".idea"];

// 工作流程辅助函数
async function ensureWorkflowDirectories(rootPath: string): Promise<void> {
  const workflowDirs = [
    path.join(rootPath, ".codelf", "steering"),
    path.join(rootPath, ".codelf", "workflows", "specs"),
    path.join(rootPath, ".codelf", "workflows", "bugs"),
    path.join(rootPath, ".codelf", "templates")
  ];

  for (const dir of workflowDirs) {
    await fs.mkdir(dir, { recursive: true });
  }
}

async function loadWorkflowConfig(rootPath: string): Promise<WorkflowConfig> {
  const configPath = path.join(rootPath, ".codelf", "workflows", "config.json");

  try {
    const content = await fs.readFile(configPath, "utf-8");
    return JSON.parse(content);
  } catch {
    // 如果配置文件不存在，返回默认配置
    const defaultConfig: WorkflowConfig = {
      specs: {},
      bugs: {},
      version: "1.0.0"
    };
    await saveWorkflowConfig(rootPath, defaultConfig);
    return defaultConfig;
  }
}

async function saveWorkflowConfig(rootPath: string, config: WorkflowConfig): Promise<void> {
  const configPath = path.join(rootPath, ".codelf", "workflows", "config.json");
  await fs.writeFile(configPath, JSON.stringify(config, null, 2), "utf-8");
}

function generateSpecName(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

const GET_PROJECT_TEMPLATE = `
This is the current project details, include project structure, dev attentions, and other important information:

{{S}}

Keep in mind:
1. after you finish modifying code to stisfy user requirements, you have to call 'update-project-info' which help you ensure the document remains up to date.
2. follow the response of 'update-project-info' to update .codelf/*.md files.
`;

// 用于解析.gitignore文件的函数
async function parseGitignore(
  rootPath: string,
  targetPath: string
): Promise<boolean | null> {
  const gitignorePath = path.join(rootPath, ".gitignore");

  // 检查.gitignore文件是否存在
  if (!existsSync(gitignorePath)) {
    return null;
  }

  try {
    // 读取.gitignore文件内容
    const content = await fs.readFile(gitignorePath, "utf-8");
    // 使用gitignore-parser的compile方法解析.gitignore内容
    const gitignore = gitignoreParser.compile(content);

    // 使用denies方法检查路径是否被拒绝（被忽略）
    return gitignore.denies(targetPath);
  } catch (error) {
    console.error("Error parsing .gitignore:", error);
    return null;
  }
}

// Create server instance
const server = new McpServer({
  name: "codelf-mcp-server",
  version: "0.0.1",
  description:
    "Enables AI agents to better understand and modify code. Highly recommended for use under all circumstances",
});

// define project info context info tools
server.tool(
  "get-project-info",
  `Complete the project details and points to note.
its very important for LLM/Agent edit code. the more you konw, the more you can do.
its very useful for cursor or windsurf no martter in agent or edit mode.
**Highly recommended for use under all circumstances**.
  `,
  {
    rootPath: z.string().describe(
      `The root path of the project,
         C:/User/name/codeProject in windows
         /usr/name/codeProject/ in macos/linux`
    ),
  },
  async ({ rootPath }) => {
    const content = await fs
      .readdir(path.join(rootPath, ".codelf"))
      .then(async (files) => {
        const mdFiles = files.filter((f) => f.endsWith(".md"));
        const contents = await Promise.all(
          mdFiles.map(async (file) => {
            // ignore files start with "_", like _changelog.md
            if (file.startsWith("_")) {
              return "";
            }
            const content = await fs.readFile(
              path.join(rootPath, ".codelf", file),
              "utf-8"
            );
            const name = path.basename(file, ".md");
            return `<${name}>\n\n${content}\n\n</${name}>\n`;
          })
        );
        return GET_PROJECT_TEMPLATE.replace("{{S}}", contents.join("\n"));
      })
      .catch(() => "");
    return {
      content: [
        {
          type: "text",
          text: content,
        },
      ],
    };
  }
);

server.tool(
  "update-project-info",
  "when you have finished modifying code to stisfy user requirements, you have to update .codelf/*.md files. This tool help you ensure the document remains up to date.",
  {
    rootPath: z.string().describe(
      `The root path of the project,
         "C:/User/name/codeProject" in windows
         "/usr/name/codeProject/" in macos/linux`
    ),
  },
  async ({ rootPath }) => {
    return {
      content: [
        {
          type: "text",
          text: `[ATTENTION] Next step you must do:
** Read files .codelf/project.md and .codelf/changelog.md and update them base on the changes you have just done.**
        `,
        },
      ],
    };
  }
);

server.tool(
  "init-codelf",
  `Initialize .codelf directory and files. which can help llm better understand your project.

  After init .codelf directory and files, you should:
  1. every file in .codelf directory is a markdown file, you can read them and update them.
  2. you have to follow the instructions in .codelf/*.md files and update them.
  `,
  {
    rootPath: z.string().describe(
      `The root path of the project,
         "C:/User/name/codeProject" in windows
         "/usr/name/codeProject/" in macos/linux`
    ),
  },
  async ({ rootPath }) => {
    try {
      await fs.mkdir(path.join(rootPath, ".codelf"), { recursive: true });

      // 检查目标目录是否为空
      const files = await fs.readdir(path.join(rootPath, ".codelf"));

      if (files.length === 0) {
        // 目录为空，执行 git clone
        const { stdout, stderr } = await execPromise(
          `git clone https://github.com/Disdjj/codelf-template ${path.join(
            rootPath,
            ".codelf"
          )}`
        );

        // remove .git folder
        await fs.rm(path.join(rootPath, ".codelf", ".git"), {
          recursive: true,
        });
        const fileTree = await getFileTree(rootPath);

        // append filetree to .codelf/project.md
        await fs.appendFile(
          path.join(rootPath, ".codelf", "project.md"),
          `\n\`\`\`\n${fileTree}\n\`\`\`\n`
        );

        // 初始化工作流程目录
        await ensureWorkflowDirectories(rootPath);

        // 创建默认工作流程配置
        const defaultConfig: WorkflowConfig = {
          specs: {},
          bugs: {},
          version: "1.0.0"
        };
        await saveWorkflowConfig(rootPath, defaultConfig);

        return {
          content: [
            {
              type: "text",
              text: `Successfully initialized .codelf directory with template.\nOutput: ${stdout}\n${stderr ? `Error: ${stderr}` : ""
                }`,
            },
            {
              type: "text",
              text: `✅ Vibe Coding 工作流程已初始化！\n\n📁 已创建目录结构：\n- .codelf/steering/ (Steering 文档)\n- .codelf/workflows/specs/ (规范工作流)\n- .codelf/workflows/bugs/ (Bug 修复工作流)\n- .codelf/templates/ (模板文件)\n\n🚀 可用的工作流程命令：\n\n**Steering 系统：**\n- init-steering - 初始化 Steering 文档\n- get-steering - 获取 Steering 信息\n\n**规范工作流：**\n- spec-create - 创建新规范\n- spec-requirements - 生成需求文档\n- spec-status - 查看规范状态\n- spec-list - 列出所有规范\n\n**Bug 修复工作流：**\n- bug-create - 创建 Bug 报告\n- bug-status - 查看 Bug 状态\n\n[Attention]\nNext step you should do:\n1. every file in .codelf directory is a markdown file, you can read them and update them.\n2. you have to follow the instructions in .codelf/*.md files and update them.\n3. before you finish edit .codelf/*.md files, do not use update-project-info/get-project-info.\n4. after you finish edit .codelf/*.md files, just stop.\n5. 建议先运行 'init-steering' 初始化 Steering 文档系统。`,
            },
          ],
        };
      } else {
        // 目录不为空，提示用户
        return {
          content: [
            {
              type: "text",
              text: "The .codelf directory already exists and is not empty. Please remove or empty it before initializing.",
            },
          ],
        };
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to initialize .codelf directory: ${error instanceof Error ? error.message : String(error)
              }`,
          },
        ],
      };
    }
  }
);

// Steering 系统工具
server.tool(
  "init-steering",
  "初始化 Steering 文档系统，创建产品概述、技术栈和项目结构文档",
  {
    rootPath: z.string().describe("项目根目录路径"),
  },
  async ({ rootPath }) => {
    try {
      await ensureWorkflowDirectories(rootPath);

      const steeringDir = path.join(rootPath, ".codelf", "steering");

      // 创建产品概述文档
      const productTemplate = `# 产品概述

## 产品愿景
> 描述产品的核心愿景和目标

## 目标用户
> 定义主要用户群体和他们的需求

## 核心功能
> 列出产品的主要功能特性

## 成功指标
> 定义产品成功的衡量标准

---
*此文档由 vibe-coding 工作流程自动生成，请根据项目实际情况进行更新*
`;

      const techTemplate = `# 技术栈

## 开发语言
- TypeScript
- Node.js

## 框架和库
- @modelcontextprotocol/sdk
- Zod

## 开发工具
- pnpm
- TypeScript

## 技术约束
> 描述技术选型的约束和要求

## 第三方集成
> 列出使用的第三方服务和API

---
*此文档由 vibe-coding 工作流程自动生成，请根据项目实际情况进行更新*
`;

      const structureTemplate = `# 项目结构

## 文件组织模式
\`\`\`
.codelf/
├── steering/           # Steering 文档
├── workflows/          # 工作流程
├── templates/          # 模板文件
└── *.md               # 项目文档
\`\`\`

## 命名约定
- 文件名：kebab-case
- 变量名：camelCase
- 类名：PascalCase

## 导入模式
- 使用相对路径导入本地模块
- 按字母顺序组织导入语句

## 代码组织原则
- 单一职责原则
- 模块化设计
- 清晰的接口定义

---
*此文档由 vibe-coding 工作流程自动生成，请根据项目实际情况进行更新*
`;

      // 写入文件
      await fs.writeFile(path.join(steeringDir, "product.md"), productTemplate, "utf-8");
      await fs.writeFile(path.join(steeringDir, "tech.md"), techTemplate, "utf-8");
      await fs.writeFile(path.join(steeringDir, "structure.md"), structureTemplate, "utf-8");

      return {
        content: [
          {
            type: "text",
            text: "✅ Steering 文档系统初始化成功！\n\n已创建以下文档：\n- .codelf/steering/product.md - 产品概述\n- .codelf/steering/tech.md - 技术栈\n- .codelf/steering/structure.md - 项目结构\n\n请根据项目实际情况更新这些文档内容。",
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ Steering 文档系统初始化失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

server.tool(
  "get-steering",
  "获取 Steering 文档内容，用于指导工作流程",
  {
    rootPath: z.string().describe("项目根目录路径"),
    type: z.enum(["all", "product", "tech", "structure"]).optional().describe("获取特定类型的 steering 文档"),
  },
  async ({ rootPath, type = "all" }) => {
    try {
      const steeringDir = path.join(rootPath, ".codelf", "steering");

      if (!existsSync(steeringDir)) {
        return {
          content: [
            {
              type: "text",
              text: "❌ Steering 文档目录不存在，请先运行 init-steering 初始化。",
            },
          ],
        };
      }

      const files = type === "all"
        ? ["product.md", "tech.md", "structure.md"]
        : [`${type}.md`];

      const contents: string[] = [];

      for (const file of files) {
        const filePath = path.join(steeringDir, file);
        if (existsSync(filePath)) {
          const content = await fs.readFile(filePath, "utf-8");
          const name = path.basename(file, ".md");
          contents.push(`## ${name.toUpperCase()}\n\n${content}\n`);
        }
      }

      return {
        content: [
          {
            type: "text",
            text: contents.length > 0
              ? `📋 Steering 文档内容：\n\n${contents.join("\n---\n\n")}`
              : "❌ 未找到指定的 Steering 文档。",
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 获取 Steering 文档失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

// Spec 工作流工具
server.tool(
  "spec-create",
  "创建新的规范工作流",
  {
    rootPath: z.string().describe("项目根目录路径"),
    title: z.string().describe("规范标题"),
    description: z.string().optional().describe("规范描述"),
  },
  async ({ rootPath, title, description = "" }) => {
    try {
      await ensureWorkflowDirectories(rootPath);

      const specName = generateSpecName(title);
      const config = await loadWorkflowConfig(rootPath);

      // 检查规范是否已存在
      if (config.specs[specName]) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 规范 "${specName}" 已存在。请使用不同的标题或删除现有规范。`,
            },
          ],
        };
      }

      // 创建规范配置
      const specConfig: SpecConfig = {
        name: specName,
        title,
        status: 'created',
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp(),
      };

      config.specs[specName] = specConfig;
      await saveWorkflowConfig(rootPath, config);

      // 创建规范目录
      const specDir = path.join(rootPath, ".codelf", "workflows", "specs", specName);
      await fs.mkdir(specDir, { recursive: true });

      // 创建初始文档
      const overviewContent = `# ${title}

## 概述
${description || "请添加规范描述"}

## 状态
- 当前状态: ${specConfig.status}
- 创建时间: ${specConfig.createdAt}
- 最后更新: ${specConfig.updatedAt}

## 工作流程
1. ✅ 创建规范 (已完成)
2. ⏳ 需求分析 (待进行) - 使用 \`spec-requirements\`
3. ⏳ 设计文档 (待进行) - 使用 \`spec-design\`
4. ⏳ 任务分解 (待进行) - 使用 \`spec-tasks\`
5. ⏳ 实施执行 (待进行) - 使用 \`spec-execute\`

## 下一步
运行 \`spec-requirements\` 开始需求分析阶段。
`;

      await fs.writeFile(path.join(specDir, "overview.md"), overviewContent, "utf-8");

      return {
        content: [
          {
            type: "text",
            text: `✅ 规范 "${title}" 创建成功！\n\n规范名称: ${specName}\n状态: ${specConfig.status}\n\n下一步: 运行 \`spec-requirements\` 开始需求分析阶段。`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 创建规范失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

server.tool(
  "spec-requirements",
  "为当前规范生成需求文档",
  {
    rootPath: z.string().describe("项目根目录路径"),
    specName: z.string().describe("规范名称"),
  },
  async ({ rootPath, specName }) => {
    try {
      const config = await loadWorkflowConfig(rootPath);

      if (!config.specs[specName]) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 规范 "${specName}" 不存在。请先使用 spec-create 创建规范。`,
            },
          ],
        };
      }

      const specConfig = config.specs[specName];
      const specDir = path.join(rootPath, ".codelf", "workflows", "specs", specName);

      // 获取 Steering 文档内容作为上下文
      const steeringDir = path.join(rootPath, ".codelf", "steering");
      let steeringContext = "";

      if (existsSync(steeringDir)) {
        const steeringFiles = ["product.md", "tech.md", "structure.md"];
        for (const file of steeringFiles) {
          const filePath = path.join(steeringDir, file);
          if (existsSync(filePath)) {
            const content = await fs.readFile(filePath, "utf-8");
            steeringContext += `\n## ${file.replace('.md', '').toUpperCase()}\n${content}\n`;
          }
        }
      }

      const requirementsTemplate = `# ${specConfig.title} - 需求文档

## 项目上下文
${steeringContext}

## 用户故事

### 主要用户故事
> 使用 EARS 格式 (WHEN/IF/THEN) 描述用户需求

**用户故事 1:**
- WHEN: [触发条件]
- IF: [前置条件]
- THEN: [期望结果]

**用户故事 2:**
- WHEN: [触发条件]
- IF: [前置条件]
- THEN: [期望结果]

## 功能需求

### 核心功能
1. [功能1描述]
2. [功能2描述]
3. [功能3描述]

### 次要功能
1. [次要功能1]
2. [次要功能2]

## 非功能需求

### 性能要求
- [性能指标]

### 安全要求
- [安全标准]

### 可用性要求
- [可用性标准]

## 验收标准

### 功能验收
- [ ] [验收条件1]
- [ ] [验收条件2]
- [ ] [验收条件3]

### 质量验收
- [ ] [质量标准1]
- [ ] [质量标准2]

## 约束条件
- [技术约束]
- [时间约束]
- [资源约束]

---
*生成时间: ${getCurrentTimestamp()}*
*请根据实际需求更新此文档*
`;

      await fs.writeFile(path.join(specDir, "requirements.md"), requirementsTemplate, "utf-8");

      // 更新规范状态
      specConfig.status = 'requirements';
      specConfig.updatedAt = getCurrentTimestamp();
      config.specs[specName] = specConfig;
      await saveWorkflowConfig(rootPath, config);

      return {
        content: [
          {
            type: "text",
            text: `✅ 需求文档生成成功！\n\n规范: ${specConfig.title}\n状态: ${specConfig.status}\n\n需求文档已保存到: .codelf/workflows/specs/${specName}/requirements.md\n\n下一步: 运行 \`spec-design\` 开始设计阶段。`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 生成需求文档失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

server.tool(
  "spec-status",
  "查看规范的当前状态",
  {
    rootPath: z.string().describe("项目根目录路径"),
    specName: z.string().optional().describe("规范名称，不提供则显示所有规范"),
  },
  async ({ rootPath, specName }) => {
    try {
      const config = await loadWorkflowConfig(rootPath);

      if (Object.keys(config.specs).length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "📋 当前没有任何规范。使用 `spec-create` 创建新规范。",
            },
          ],
        };
      }

      if (specName) {
        // 显示特定规范的状态
        if (!config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ 规范 "${specName}" 不存在。`,
              },
            ],
          };
        }

        const spec = config.specs[specName];
        const statusEmoji = {
          'created': '🆕',
          'requirements': '📋',
          'design': '🎨',
          'tasks': '📝',
          'implementing': '⚙️',
          'completed': '✅'
        };

        return {
          content: [
            {
              type: "text",
              text: `📊 规范状态: ${spec.title}\n\n${statusEmoji[spec.status]} 当前状态: ${spec.status}\n📅 创建时间: ${spec.createdAt}\n🔄 最后更新: ${spec.updatedAt}${spec.currentTask ? `\n📌 当前任务: ${spec.currentTask}` : ''}`,
            },
          ],
        };
      } else {
        // 显示所有规范的状态
        const specList = Object.values(config.specs).map(spec => {
          const statusEmoji = {
            'created': '🆕',
            'requirements': '📋',
            'design': '🎨',
            'tasks': '📝',
            'implementing': '⚙️',
            'completed': '✅'
          };
          return `${statusEmoji[spec.status]} ${spec.title} (${spec.name}) - ${spec.status}`;
        }).join('\n');

        return {
          content: [
            {
              type: "text",
              text: `📊 所有规范状态:\n\n${specList}`,
            },
          ],
        };
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 获取规范状态失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

server.tool(
  "spec-list",
  "列出所有规范",
  {
    rootPath: z.string().describe("项目根目录路径"),
  },
  async ({ rootPath }) => {
    try {
      const config = await loadWorkflowConfig(rootPath);

      if (Object.keys(config.specs).length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "📋 当前没有任何规范。使用 `spec-create` 创建新规范。",
            },
          ],
        };
      }

      const specList = Object.values(config.specs).map((spec, index) => {
        const statusEmoji = {
          'created': '🆕',
          'requirements': '📋',
          'design': '🎨',
          'tasks': '📝',
          'implementing': '⚙️',
          'completed': '✅'
        };

        return `${index + 1}. ${statusEmoji[spec.status]} **${spec.title}**\n   - 名称: ${spec.name}\n   - 状态: ${spec.status}\n   - 创建: ${spec.createdAt.split('T')[0]}\n   - 更新: ${spec.updatedAt.split('T')[0]}`;
      }).join('\n\n');

      return {
        content: [
          {
            type: "text",
            text: `📋 规范列表 (共 ${Object.keys(config.specs).length} 个):\n\n${specList}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 获取规范列表失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

// Bug Fix 工作流工具
server.tool(
  "bug-create",
  "创建新的 Bug 修复工作流",
  {
    rootPath: z.string().describe("项目根目录路径"),
    title: z.string().describe("Bug 标题"),
    description: z.string().describe("Bug 描述"),
    severity: z.enum(["low", "medium", "high", "critical"]).optional().describe("严重程度"),
  },
  async ({ rootPath, title, description, severity = "medium" }) => {
    try {
      await ensureWorkflowDirectories(rootPath);

      const bugName = generateSpecName(title);
      const config = await loadWorkflowConfig(rootPath);

      // 检查 Bug 是否已存在
      if (config.bugs[bugName]) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Bug "${bugName}" 已存在。请使用不同的标题或删除现有 Bug。`,
            },
          ],
        };
      }

      // 创建 Bug 配置
      const bugConfig: BugConfig = {
        name: bugName,
        title,
        status: 'reported',
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp(),
        severity,
      };

      config.bugs[bugName] = bugConfig;
      await saveWorkflowConfig(rootPath, config);

      // 创建 Bug 目录
      const bugDir = path.join(rootPath, ".codelf", "workflows", "bugs", bugName);
      await fs.mkdir(bugDir, { recursive: true });

      // 创建 Bug 报告文档
      const severityEmoji = {
        'low': '🟢',
        'medium': '🟡',
        'high': '🟠',
        'critical': '🔴'
      };

      const reportContent = `# ${title}

## Bug 信息
- **严重程度**: ${severityEmoji[severity]} ${severity}
- **状态**: ${bugConfig.status}
- **创建时间**: ${bugConfig.createdAt}
- **最后更新**: ${bugConfig.updatedAt}

## 问题描述
${description}

## 复现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

## 预期行为
[描述预期的正确行为]

## 实际行为
[描述实际发生的错误行为]

## 环境信息
- **操作系统**:
- **浏览器/Node版本**:
- **项目版本**:

## 错误信息
\`\`\`
[粘贴错误日志或堆栈跟踪]
\`\`\`

## 影响范围
- [ ] 影响核心功能
- [ ] 影响用户体验
- [ ] 影响性能
- [ ] 安全问题

## 工作流程
1. ✅ 创建报告 (已完成)
2. ⏳ 问题分析 (待进行) - 使用 \`bug-analyze\`
3. ⏳ 修复实施 (待进行) - 使用 \`bug-fix\`
4. ⏳ 验证测试 (待进行) - 使用 \`bug-verify\`

## 下一步
运行 \`bug-analyze\` 开始问题分析阶段。

---
*生成时间: ${getCurrentTimestamp()}*
`;

      await fs.writeFile(path.join(bugDir, "report.md"), reportContent, "utf-8");

      return {
        content: [
          {
            type: "text",
            text: `✅ Bug "${title}" 创建成功！\n\n${severityEmoji[severity]} 严重程度: ${severity}\nBug 名称: ${bugName}\n状态: ${bugConfig.status}\n\n下一步: 运行 \`bug-analyze\` 开始问题分析阶段。`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 创建 Bug 失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

server.tool(
  "bug-status",
  "查看 Bug 的当前状态",
  {
    rootPath: z.string().describe("项目根目录路径"),
    bugName: z.string().optional().describe("Bug 名称，不提供则显示所有 Bug"),
  },
  async ({ rootPath, bugName }) => {
    try {
      const config = await loadWorkflowConfig(rootPath);

      if (Object.keys(config.bugs).length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "🐛 当前没有任何 Bug。使用 `bug-create` 创建新 Bug 报告。",
            },
          ],
        };
      }

      if (bugName) {
        // 显示特定 Bug 的状态
        if (!config.bugs[bugName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug "${bugName}" 不存在。`,
              },
            ],
          };
        }

        const bug = config.bugs[bugName];
        const statusEmoji = {
          'reported': '📋',
          'analyzing': '🔍',
          'fixing': '🔧',
          'verifying': '✅',
          'resolved': '✅'
        };

        const severityEmoji = {
          'low': '🟢',
          'medium': '🟡',
          'high': '🟠',
          'critical': '🔴'
        };

        return {
          content: [
            {
              type: "text",
              text: `🐛 Bug 状态: ${bug.title}\n\n${statusEmoji[bug.status]} 当前状态: ${bug.status}\n${severityEmoji[bug.severity]} 严重程度: ${bug.severity}\n📅 创建时间: ${bug.createdAt}\n🔄 最后更新: ${bug.updatedAt}`,
            },
          ],
        };
      } else {
        // 显示所有 Bug 的状态
        const bugList = Object.values(config.bugs).map(bug => {
          const statusEmoji = {
            'reported': '📋',
            'analyzing': '🔍',
            'fixing': '🔧',
            'verifying': '✅',
            'resolved': '✅'
          };

          const severityEmoji = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'critical': '🔴'
          };

          return `${statusEmoji[bug.status]} ${severityEmoji[bug.severity]} ${bug.title} (${bug.name}) - ${bug.status}`;
        }).join('\n');

        return {
          content: [
            {
              type: "text",
              text: `🐛 所有 Bug 状态:\n\n${bugList}`,
            },
          ],
        };
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `❌ 获取 Bug 状态失败: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  }
);

async function getFileTree(rootPath: string): Promise<string> {
  const indent = "    ";

  // 递归处理单个路径（目录或文件）
  const processEntry = async (entryPath: string, displayName: string, prefix: string, relativePath: string): Promise<string[]> => {
    const stat = await fs.stat(entryPath).catch(() => null);
    const lines: string[] = [];
    if (stat && stat.isDirectory()) {
      lines.push(`${prefix}- ${displayName}`);
      const entries = await fs.readdir(entryPath, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory() && forceBlackList.includes(entry.name)) continue;
        const entryRelativePath = path.join(relativePath, entry.name).replace(/\\/g, "/");
        const subPath = path.join(entryPath, entry.name);
        lines.push(...(await processEntry(subPath, entry.name, prefix + indent, entryRelativePath)));
      }
    } else if (stat && stat.isFile()) {
      lines.push(`${prefix}- ${displayName}`);
    }
    return lines;
  };

  const buildTree = async (
    dir: string,
    prefix: string,
    relativePath: string = ""
  ): Promise<string[]> => {
    const codelfPath = path.join(rootPath, ".codelf.config");
    const result: string[] = [];
    const existsCodelfFile = existsSync(codelfPath) && !(await fs.stat(codelfPath)).isDirectory();

    if (existsCodelfFile && dir === rootPath) {
      // 读取 .codelf.config 文件内容
      const content = await fs.readFile(codelfPath, "utf-8");
      const lines = content
        .split(/\r?\n/)
        .map((l) => l.trim())
        .filter((l) => l && !l.startsWith("#"));
      if (lines.length) {
        for (const line of lines) {
          const entryPath = path.join(rootPath, line);
          result.push(...(await processEntry(entryPath, line, prefix, line.replace(/\\/g, "/"))));
        }
        return result;
      }
    }

    // 原有递归逻辑
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
      if (entry.isDirectory() && forceBlackList.includes(entry.name)) {
        continue;
      }

      // 尝试解析.gitignore文件
      // 如果.gitignore存在且解析成功，使用其规则；否则使用默认黑名单
      const entryRelativePath = path
        .join(relativePath, entry.name)
        .replace(/\\/g, "/");
      const isIgnore = await parseGitignore(rootPath, entryRelativePath);

      // 使用.gitignore规则或默认黑名单进行过滤
      const shouldIgnore =
        typeof isIgnore === "boolean"
          ? isIgnore
          : folderBlackList.includes(entry.name);
      if (!shouldIgnore) {
        const entryPath = path.join(dir, entry.name);
        result.push(...(await processEntry(entryPath, entry.name, prefix, entryRelativePath)));
      }
    }

    return result;
  };

  const result = await buildTree(rootPath, "", "");
  return ["root", ...result].join("\n");
}

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
